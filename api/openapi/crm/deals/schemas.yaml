# Deal schemas
Deal:
  type: object
  properties:
    id:
      type: string
      format: uuid
    title:
      type: string
    description:
      type: string
    estimated_value:
      type: number
    company_id:
      type: string
      format: uuid
    deal_stage_id:
      type: string
      format: uuid
    expected_close_date:
      type: string
      format: date
    created_by:
      type: string
      format: uuid
    created_at:
      type: string
      format: date-time
    updated_at:
      type: string
      format: date-time

DealWithDetails:
  allOf:
    - $ref: '#/Deal'
    - type: object
      properties:
        company:
          $ref: '#/DealCompanyInfo'
        deal_stage:
          $ref: '#/DealStageInfo'

DealCompanyInfo:
  type: object
  properties:
    id:
      type: string
      format: uuid
    name:
      type: string

DealStageInfo:
  type: object
  properties:
    id:
      type: string
      format: uuid
    name:
      type: string
    pipeline_order:
      type: integer

DealDetails:
  allOf:
    - $ref: '#/Deal'
    - type: object
      properties:
        company:
          type: object
          properties:
            id:
              type: string
              format: uuid
            name:
              type: string
            website:
              type: string
            phone:
              type: string
        deal_stage:
          type: object
          properties:
            id:
              type: string
              format: uuid
            name:
              type: string
            pipeline_order:
              type: integer
            is_closed_won:
              type: boolean
            is_closed_lost:
              type: boolean

DealCreate:
  type: object
  properties:
    title:
      type: string
      minLength: 1
    description:
      type: string
    estimated_value:
      type: number
      minimum: 0
    company_id:
      type: string
      format: uuid
    deal_stage_id:
      type: string
      format: uuid
    expected_close_date:
      type: string
      format: date
  required:
    - title
    - company_id
    - deal_stage_id

DealUpdate:
  type: object
  properties:
    title:
      type: string
      minLength: 1
    description:
      type: string
    estimated_value:
      type: number
      minimum: 0
    company_id:
      type: string
      format: uuid
    deal_stage_id:
      type: string
      format: uuid
    expected_close_date:
      type: string
      format: date

DealStage:
  type: object
  properties:
    id:
      type: string
      format: uuid
    name:
      type: string
    pipeline_order:
      type: integer
    is_closed_won:
      type: boolean
      default: false
    is_closed_lost:
      type: boolean
      default: false
    created_at:
      type: string
      format: date-time