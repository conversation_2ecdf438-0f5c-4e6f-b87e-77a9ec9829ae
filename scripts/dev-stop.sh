#!/bin/bash

# Development Environment Stop Script
# Stop all Docker services

set -e

# Set up Bazel runfiles and copy files to working directory
if [[ -n "${RUNFILES_DIR:-}" ]]; then
  # Running under Bazel - copy files to a temp directory
  WORK_DIR=$(mktemp -d)
  echo "Setting up working directory: $WORK_DIR"
  
  # Copy docker-compose files from runfiles
  cp "$RUNFILES_DIR/_main/docker-compose.yml" "$WORK_DIR/" 2>/dev/null || echo "docker-compose.yml not found in runfiles"
  cp "$RUNFILES_DIR/_main/docker-compose.dev.yml" "$WORK_DIR/" 2>/dev/null || echo "docker-compose.dev.yml not found in runfiles"
  
  cd "$WORK_DIR"
  
  # Cleanup function
  cleanup() {
    echo "Cleaning up temporary directory: $WORK_DIR"
    rm -rf "$WORK_DIR"
  }
  trap cleanup EXIT
else
  # Running directly
  cd "$(dirname "$0")/.."
fi

echo "=== Stopping Orbit Development Environment ==="

echo "Stopping Docker services..."
# Stop all orbit containers regardless of project name
docker stop $(docker ps -q --filter "name=orbit-*") 2>/dev/null || true
docker rm $(docker ps -aq --filter "name=orbit-*") 2>/dev/null || true

# Also try the standard docker-compose commands
docker-compose down 2>/dev/null || true
docker-compose -f docker-compose.dev.yml down 2>/dev/null || true

echo "✅ Docker services stopped"

echo "Stopping local services..."
# Stop auth service (port 8004)
AUTH_PID=$(lsof -ti:8004 2>/dev/null)
if [[ -n "$AUTH_PID" ]]; then
    kill $AUTH_PID 2>/dev/null || true
    echo "  ✅ Auth service stopped (PID: $AUTH_PID)"
else
    echo "  ℹ️  Auth service not running"
fi

# Stop CRM backend (port 8005)
CRM_PID=$(lsof -ti:8005 2>/dev/null)
if [[ -n "$CRM_PID" ]]; then
    kill $CRM_PID 2>/dev/null || true
    echo "  ✅ CRM backend stopped (PID: $CRM_PID)"
else
    echo "  ℹ️  CRM backend not running"
fi

# Stop ProjectHub backend (port 8003)
HUB_PID=$(lsof -ti:8003 2>/dev/null)
if [[ -n "$HUB_PID" ]]; then
    kill $HUB_PID 2>/dev/null || true
    echo "  ✅ ProjectHub backend stopped (PID: $HUB_PID)"
else
    echo "  ℹ️  ProjectHub backend not running"
fi

# Stop CRM frontend (port 8081)
CRM_FRONTEND_PID=$(lsof -ti:8081 2>/dev/null)
if [[ -n "$CRM_FRONTEND_PID" ]]; then
    kill $CRM_FRONTEND_PID 2>/dev/null || true
    echo "  ✅ CRM frontend stopped (PID: $CRM_FRONTEND_PID)"
else
    echo "  ℹ️  CRM frontend not running"
fi

# Stop ProjectHub frontend (port 8082)
HUB_FRONTEND_PID=$(lsof -ti:8082 2>/dev/null)
if [[ -n "$HUB_FRONTEND_PID" ]]; then
    kill $HUB_FRONTEND_PID 2>/dev/null || true
    echo "  ✅ ProjectHub frontend stopped (PID: $HUB_FRONTEND_PID)"
else
    echo "  ℹ️  ProjectHub frontend not running"
fi

echo ""
echo "✅ All services stopped (Docker + Local)"