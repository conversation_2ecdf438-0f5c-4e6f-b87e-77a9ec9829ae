companies:
  get:
    tags: [Companies]
    summary: List companies
    description: Retrieve a list of all active companies
    parameters:
      - name: status_id
        in: query
        schema:
          type: string
          format: uuid
        description: Filter by company status
      - name: include_deleted
        in: query
        schema:
          type: boolean
          default: false
        description: Include soft-deleted companies
    responses:
      '200':
        description: List of companies
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: './schemas.yaml#/Company'

  post:
    tags: [Companies]
    summary: Create company
    description: Create a new company
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/CompanyCreate'
    responses:
      '201':
        description: Company created successfully
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/Company'
      '400':
        description: Invalid input

companies_by_id:
  get:
    tags: [Companies]
    summary: Get company
    description: Retrieve a specific company by ID
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '200':
        description: Company details
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/CompanyDetails'
      '404':
        description: Company not found

  put:
    tags: [Companies]
    summary: Update company
    description: Update an existing company
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/CompanyUpdate'
    responses:
      '200':
        description: Company updated successfully
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/Company'
      '404':
        description: Company not found

  delete:
    tags: [Companies]
    summary: Delete company
    description: Soft delete a company (sets is_deleted to true)
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '204':
        description: Company deleted successfully
      '404':
        description: Company not found

companies_status:
  put:
    tags: [Companies]
    summary: Update company status
    description: Update the status of a company
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            title: CompanyStatusUpdateRequest
            properties:
              company_status_id:
                type: string
                format: uuid
            required:
              - company_status_id
    responses:
      '200':
        description: Company status updated successfully
      '404':
        description: Company not found

company_statuses:
  get:
    tags: [Companies]
    summary: List company statuses
    description: Retrieve all company statuses ordered by pipeline order
    responses:
      '200':
        description: List of company statuses
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: './schemas.yaml#/CompanyStatus'