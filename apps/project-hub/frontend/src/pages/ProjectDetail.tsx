import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { apiClient } from "@/lib/api-client";
import { useToast } from "@/hooks/use-toast";
import { useNavigateWithFilters } from "@/hooks/useNavigateWithFilters";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { TaskItem } from "@/components/tasks/TaskItem";
import { TaskForm } from "@/components/tasks/TaskForm";
import { ProjectDeleteButton } from "@/components/projects/ProjectDeleteButton";
import { ConvertToTaskButton } from "@/components/projects/ConvertToTaskButton";
import { ArchiveProjectButton } from "@/components/projects/ArchiveProjectButton";
import { UnarchiveProjectButton } from "@/components/projects/UnarchiveProjectButton";
import { ProjectStatusDropdown } from "@/components/projects/ProjectStatusDropdown";
import { PrioritySelector } from "@/components/projects/PrioritySelector";
import { ProjectKeyMetrics } from "@/components/projects/ProjectKeyMetrics";
import { ProjectTeamCard } from "@/components/projects/ProjectTeamCard";
import { ProjectTimelineCard } from "@/components/projects/ProjectTimelineCard";
import { ProjectDocumentsCard } from "@/components/projects/ProjectDocumentsCard";
import { ArrowLeft, Edit, Plus, Loader2, AlertCircle, Calendar, User, Building2, Clock, AlertTriangle, CheckCircle2, Circle, Trophy, Sparkles, ExternalLink, Workflow } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { ProjectWithDetails, Task, SubTask, TaskWithSubTasks } from "@/types/project";
import { cn } from "@/lib/utils";
import { differenceInDays, format } from "date-fns";
import { PROJECT_STATUSES } from "@/lib/constants";

export default function ProjectDetail() {
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { navigateBack, navigateToProjectEdit } = useNavigateWithFilters();
  
  const [isTaskDialogOpen, setIsTaskDialogOpen] = useState(false);
  const [isSubTaskDialogOpen, setIsSubTaskDialogOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [editingSubTask, setEditingSubTask] = useState<SubTask | null>(null);
  const [parentTaskId, setParentTaskId] = useState<string | null>(null);
  const [showProjectCompletionDialog, setShowProjectCompletionDialog] = useState(false);

  const { data: project, isLoading, error } = useQuery({
    queryKey: ['project-detail', id],
    queryFn: async () => {
      if (!id) throw new Error('Project ID is required');
      
      const projectData = await apiClient.getProject(id);
      
      const result: ProjectWithDetails = {
        ...projectData as any,
        tasks: projectData.tasks as TaskWithSubTasks[],
        progress: 0 // Will be calculated from tasks
      };

      return result;
    },
    enabled: !!id,
  });

  const createTaskMutation = useMutation({
    mutationFn: async (data: any) => {
      if (!id) throw new Error('Project ID is required');
      
      // Get assignee name if assignee_id is provided
      let assignee = data.assignee;
      if (data.assignee_id && !assignee) {
        const teamMembers = await apiClient.getTeamMembers();
        const teamMember = teamMembers.find((tm: any) => tm.id === data.assignee_id);
        assignee = teamMember?.name || '';
      }
      
      return await apiClient.createTask({ ...data, project_id: id, assignee });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['project-detail', id] });
      queryClient.invalidateQueries({ queryKey: ['project-progress'] });
      setIsTaskDialogOpen(false);
      setEditingTask(null);
      toast({
        title: "Success",
        description: "Task created successfully!",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create task. Please try again.",
        variant: "destructive",
      });
      console.error('Error creating task:', error);
    },
  });

  const updateTaskMutation = useMutation({
    mutationFn: async ({ taskId, data }: { taskId: string; data: any }) => {
      // Get assignee name if assignee_id is provided
      let assignee = data.assignee;
      if (data.assignee_id && !assignee) {
        const teamMembers = await apiClient.getTeamMembers();
        const teamMember = teamMembers.find((tm: any) => tm.id === data.assignee_id);
        assignee = teamMember?.name || '';
      }

      return await apiClient.updateTask(taskId, { ...data, assignee });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['project-detail', id] });
      queryClient.invalidateQueries({ queryKey: ['project-progress'] });
      setIsTaskDialogOpen(false);
      setEditingTask(null);
      toast({
        title: "Success",
        description: "Task updated successfully!",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update task. Please try again.",
        variant: "destructive",
      });
      console.error('Error updating task:', error);
    },
  });

  const createSubTaskMutation = useMutation({
    mutationFn: async (data: any) => {
      if (!parentTaskId) throw new Error('Parent task ID is required');
      
      // Get assignee name if assignee_id is provided
      let assignee = data.assignee;
      if (data.assignee_id && !assignee) {
        const teamMembers = await apiClient.getTeamMembers();
        const teamMember = teamMembers.find((tm: any) => tm.id === data.assignee_id);
        assignee = teamMember?.name || '';
      }
      
      return await apiClient.createSubTask({ ...data, task_id: parentTaskId, assignee });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['project-detail', id] });
      queryClient.invalidateQueries({ queryKey: ['project-progress'] });
      setIsSubTaskDialogOpen(false);
      setEditingSubTask(null);
      setParentTaskId(null);
      toast({
        title: "Success",
        description: "Sub-task created successfully!",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create sub-task. Please try again.",
        variant: "destructive",
      });
      console.error('Error creating sub-task:', error);
    },
  });

  const updateSubTaskMutation = useMutation({
    mutationFn: async ({ subTaskId, data }: { subTaskId: string; data: any }) => {
      // Get assignee name if assignee_id is provided
      let assignee = data.assignee;
      if (data.assignee_id && !assignee) {
        const teamMembers = await apiClient.getTeamMembers();
        const teamMember = teamMembers.find((tm: any) => tm.id === data.assignee_id);
        assignee = teamMember?.name || '';
      }

      return await apiClient.updateSubTask(subTaskId, { ...data, assignee });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['project-detail', id] });
      queryClient.invalidateQueries({ queryKey: ['project-progress'] });
      setIsSubTaskDialogOpen(false);
      setEditingSubTask(null);
      toast({
        title: "Success",
        description: "Sub-task updated successfully!",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update sub-task. Please try again.",
        variant: "destructive",
      });
      console.error('Error updating sub-task:', error);
    },
  });

  const deleteTaskMutation = useMutation({
    mutationFn: async (taskId: string) => {
      return await apiClient.deleteTask(taskId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['project-detail', id] });
      queryClient.invalidateQueries({ queryKey: ['project-progress'] });
      toast({
        title: "Success",
        description: "Task deleted successfully!",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete task. Please try again.",
        variant: "destructive",
      });
      console.error('Error deleting task:', error);
    },
  });

  const updateProjectMutation = useMutation({
    mutationFn: async (data: any) => {
      if (!id) throw new Error('Project ID is required');
      
      return await apiClient.updateProject(id, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['project-detail', id] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast({
        title: "Success",
        description: "Project updated successfully!",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update project. Please try again.",
        variant: "destructive",
      });
      console.error('Error updating project:', error);
    },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'backlog':
        return 'status-backlog';
      case 'not-started':
        return 'status-not-started';
      case 'in-progress':
        return 'status-in-progress';
      case 'completed':
        return 'status-completed';
      default:
        return 'status-backlog';
    }
  };

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setIsTaskDialogOpen(true);
  };

  const handleEditSubTask = (subTask: SubTask) => {
    setEditingSubTask(subTask);
    setIsSubTaskDialogOpen(true);
  };

  const handleAddSubTask = (taskId: string) => {
    setParentTaskId(taskId);
    setIsSubTaskDialogOpen(true);
  };

  const handleTaskSubmit = (data: any) => {
    if (editingTask) {
      updateTaskMutation.mutate({ taskId: editingTask.id, data });
    } else {
      createTaskMutation.mutate(data);
    }
  };

  const handleSubTaskSubmit = (data: any) => {
    if (editingSubTask) {
      updateSubTaskMutation.mutate({ subTaskId: editingSubTask.id, data });
    } else {
      createSubTaskMutation.mutate(data);
    }
  };

  const handleCompleteTask = (task: Task) => {
    const newStatus = task.status === 'done' ? 'to-do' : 'done';
    updateTaskMutation.mutate({ 
      taskId: task.id, 
      data: { status: newStatus } 
    });
  };

  const handleCompleteSubTask = (subTask: SubTask) => {
    const newStatus = subTask.status === 'done' ? 'to-do' : 'done';
    updateSubTaskMutation.mutate({ 
      subTaskId: subTask.id, 
      data: { status: newStatus } 
    });
  };

  const handleStatusChange = (item: Task | SubTask, status: string) => {
    if ('project_id' in item) {
      // It's a Task
      updateTaskMutation.mutate({ 
        taskId: item.id, 
        data: { status } 
      });
    } else {
      // It's a SubTask
      updateSubTaskMutation.mutate({ 
        subTaskId: item.id, 
        data: { status } 
      });
    }
  };

  const handleCompleteProject = () => {
    const allTasks = project?.tasks || [];
    const incompleteTasks = allTasks.filter(task => task.status !== 'done');
    const incompleteSubTasks = allTasks.flatMap(task => 
      (task.sub_tasks || []).filter(subTask => subTask.status !== 'done')
    );
    
    if (project?.status !== 'completed' && (incompleteTasks.length > 0 || incompleteSubTasks.length > 0)) {
      setShowProjectCompletionDialog(true);
    } else {
      const newStatus = project?.status === 'completed' ? 'in-progress' : 'completed';
      updateProjectMutation.mutate({ status: newStatus });
    }
  };

  const handleConfirmProjectComplete = () => {
    updateProjectMutation.mutate({ status: 'completed' });
    setShowProjectCompletionDialog(false);
  };

  const handleOpenPRDDocument = () => {
    if (project?.prd_document_link) {
      window.open(project.prd_document_link, '_blank', 'noopener,noreferrer');
    }
  };

  const handleOpenPOCUrl = () => {
    if (project?.poc_url) {
      window.open(project.poc_url, '_blank', 'noopener,noreferrer');
    }
  };

  const handleDeleteTask = (task: Task) => {
    deleteTaskMutation.mutate(task.id);
  };

  const getTimelineStatus = () => {
    // Guard clause: return null if project isn't loaded yet or end_date is missing
    if (!project || !project.end_date) return null;
    
    const endDate = new Date(project.end_date);
    const today = new Date();
    const daysUntilEnd = differenceInDays(endDate, today);
    
    if (project.status === 'completed') {
      return { status: 'completed', message: 'Project Completed', color: 'text-green-600', bgColor: 'bg-green-50' };
    }
    
    if (daysUntilEnd < 0) {
      return { 
        status: 'overdue', 
        message: `${Math.abs(daysUntilEnd)} days overdue`, 
        color: 'text-red-600', 
        bgColor: 'bg-red-50' 
      };
    }
    
    if (daysUntilEnd <= 7) {
      return { 
        status: 'warning', 
        message: `${daysUntilEnd} days remaining`, 
        color: 'text-orange-600', 
        bgColor: 'bg-orange-50' 
      };
    }
    
    return { 
      status: 'on-track', 
      message: `${daysUntilEnd} days remaining`, 
      color: 'text-gray-600', 
      bgColor: 'bg-gray-50' 
    };
  };

  // Only call getTimelineStatus if project is loaded
  const timelineStatus = project ? getTimelineStatus() : null;

  // Only check for date changes if project is loaded
  const hasDateChanges = project?.original_end_date && project?.end_date && 
    project.original_end_date !== project.end_date;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !project) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load project. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  const isArchived = project.status === 'archived';

  return (
    <div className="space-y-6">
      {/* Archive Status Banner */}
      {isArchived && (
        <Alert className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
          <AlertCircle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800 dark:text-orange-200">
            This project is archived. You cannot make edits while it's archived. To make changes, please restore the project first.
          </AlertDescription>
        </Alert>
      )}

      {/* Header Section */}
      <div className="flex items-center gap-4">
        <Button 
          variant="outline" 
          size="icon" 
          onClick={navigateBack}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex-1">
          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
            <Building2 className="h-4 w-4" />
            <span>{project.company_name}</span>
          </div>
          <div className="flex items-center gap-3 mb-2">
            <h1 className="text-3xl font-bold tracking-tight">{project.name}</h1>
            <Badge variant="outline" className="text-xs">
              {project.type === 'external' ? 'External' : 'Internal'}
            </Badge>
            {project.status === 'completed' ? (
              <>
                {(() => {
                  const incompleteTasks = project.tasks?.filter(task => task.status !== 'done') || [];
                  const incompleteSubTasks = project.tasks?.flatMap(task => 
                    (task.sub_tasks || []).filter(subTask => subTask.status !== 'done')
                  ) || [];
                  const totalIncomplete = incompleteTasks.length + incompleteSubTasks.length;
                  
                  if (totalIncomplete > 0) {
                    return (
                      <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 animate-fade-in">
                        <CheckCircle2 className="w-3 h-3 mr-1" />
                        {totalIncomplete} task{totalIncomplete > 1 ? 's' : ''} incomplete
                      </Badge>
                    );
                  } else {
                    return (
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 animate-fade-in">
                        <CheckCircle2 className="w-3 h-3 mr-1" />
                        All tasks done
                      </Badge>
                    );
                  }
                })()}
              </>
            ) : (
              <ProjectStatusDropdown 
                projectId={project.id}
                currentStatus={project.status}
                className="text-sm"
                disabled={isArchived}
              />
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          {isArchived && (
            <UnarchiveProjectButton
              projectId={project.id}
              projectName={project.name}
              variant="default"
              size="sm"
            />
          )}
          {!isArchived && (
            <ConvertToTaskButton 
              project={project} 
              variant="outline"
              size="sm"
            />
          )}
          {project.status === 'completed' && !isArchived && (
            <ArchiveProjectButton
              projectId={project.id}
              projectName={project.name}
              variant="outline"
              className="text-sm"
            />
          )}
          {!isArchived && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleCompleteProject}
              className="flex items-center gap-2"
            >
              {project.status === 'completed' ? (
                <>
                  <Circle className="h-4 w-4" />
                  Mark Incomplete
                </>
              ) : (
                <>
                  <CheckCircle2 className="h-4 w-4" />
                  Complete Project
                </>
              )}
            </Button>
          )}
          {!isArchived && (
            <Button asChild variant="outline">
              <Link to={`/projects/${id}/sdlc`}>
                <Workflow className="h-4 w-4 mr-2" />
                SDLC View
              </Link>
            </Button>
          )}
          {!isArchived && (
            <Button onClick={() => navigateToProjectEdit(id!)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Project
            </Button>
          )}
          <ProjectDeleteButton 
            project={project} 
            variant="button"
            tasks={project.tasks || []}
          />
        </div>
      </div>

      {/* Key Metrics Bar */}
      <ProjectKeyMetrics project={project} />

      {/* Timeline Status Alert - Only for critical statuses */}
      {timelineStatus && timelineStatus.status === 'completed' && (
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-green-100/20 to-emerald-100/20"></div>
          <div className="relative z-10 flex items-center">
            <div className="flex items-center justify-center w-8 h-8 bg-green-500 rounded-full animate-pulse mr-3">
              <Trophy className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <p className="font-bold text-green-800">PROJECT COMPLETED!</p>
                <Sparkles className="w-4 h-4 text-green-600 animate-bounce" />
              </div>
              {project.completed_at && (
                <div className="mt-2">
                  <div className="flex items-center text-sm">
                    <CheckCircle2 className="w-4 h-4 mr-2 text-green-600" />
                    <span className="text-green-700 font-medium">Completed on:</span>
                    <span className="ml-2 font-bold text-green-800">
                      {format(new Date(project.completed_at), "EEEE, MMMM d, yyyy 'at' h:mm a")}
                    </span>
                  </div>
                  <div className="mt-1 text-xs text-green-600/80 animate-fade-in">
                    🎉 Congratulations on completing this project!
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Main Content Grid - Two Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Content (2/3 width) */}
        <div className="lg:col-span-2 space-y-6">
          {/* Project Description */}
          {project.description && (
            <div className="bg-card border rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-3">Description</h3>
              <p className="text-muted-foreground leading-relaxed">{project.description}</p>
            </div>
          )}

          {/* Tasks Section */}
          <div className="bg-card border rounded-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold">Tasks</h2>
              {!isArchived && (
                <Button onClick={() => setIsTaskDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Task
                </Button>
              )}
            </div>

            {project.tasks && project.tasks.length > 0 ? (
              <div className="space-y-4">
                {project.tasks.map((task) => (
                    <TaskItem
                      key={task.id}
                      task={task}
                      subTasks={task.sub_tasks || []}
                      onEditTask={handleEditTask}
                      onAddSubTask={handleAddSubTask}
                      onEditSubTask={handleEditSubTask}
                      onCompleteTask={handleCompleteTask}
                      onCompleteSubTask={handleCompleteSubTask}
                      onStatusChange={handleStatusChange}
                      onDeleteTask={handleDeleteTask}
                      disabled={isArchived}
                    />
                ))}
              </div>
            ) : (
              <div className="text-center py-12 border border-dashed rounded-lg">
                <p className="text-muted-foreground mb-4">No tasks created yet.</p>
                {!isArchived && (
                  <Button onClick={() => setIsTaskDialogOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create First Task
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Right Column - Project Details (1/3 width) */}
        <div className="space-y-6">
          {/* Team Card */}
          <ProjectTeamCard project={project} />

          {/* Timeline Card */}
          <ProjectTimelineCard project={project} />

          {/* Project Metadata */}
          <div className="bg-card border rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Priority Details
            </h3>
            <PrioritySelector
              priority={project.priority_level || 'P3'}
              effort={project.effort_estimate || 'M'}
              impact={project.impact_type || 'Platform'}
              impactTypeId={project.impact_type_id}
              onPriorityChange={!isArchived ? (priority) => {
                updateProjectMutation.mutate({ priority_level: priority });
              } : undefined}
              onEffortChange={!isArchived ? (effort) => {
                updateProjectMutation.mutate({ effort_estimate: effort });
              } : undefined}
              onImpactChange={!isArchived ? (impact) => {
                updateProjectMutation.mutate({ impact_type: impact });
              } : undefined}
              onImpactTypeIdChange={!isArchived ? (impactTypeId) => {
                updateProjectMutation.mutate({ impact_type_id: impactTypeId });
              } : undefined}
              disabled={isArchived}
            />
          </div>

          {/* Documents Card */}
          <ProjectDocumentsCard project={project} />
        </div>
      </div>


      {/* Task Dialog */}
      <Dialog open={isTaskDialogOpen} onOpenChange={(open) => {
        setIsTaskDialogOpen(open);
        if (!open) {
          setEditingTask(null);
        }
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingTask ? 'Edit Task' : 'Create New Task'}
            </DialogTitle>
          </DialogHeader>
          <TaskForm
            task={editingTask || undefined}
            onSubmit={handleTaskSubmit}
            onCancel={() => {
              setIsTaskDialogOpen(false);
              setEditingTask(null);
            }}
            isLoading={createTaskMutation.isPending || updateTaskMutation.isPending}
            projectEndDate={project?.end_date || undefined}
            onUpdateProjectEndDate={(newEndDate) => {
              updateProjectMutation.mutate({ end_date: newEndDate });
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Sub-task Dialog */}
      <Dialog open={isSubTaskDialogOpen} onOpenChange={(open) => {
        setIsSubTaskDialogOpen(open);
        if (!open) {
          setEditingSubTask(null);
          setParentTaskId(null);
        }
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingSubTask ? 'Edit Sub-task' : 'Create New Sub-task'}
            </DialogTitle>
          </DialogHeader>
          <TaskForm
            task={editingSubTask || undefined}
            onSubmit={handleSubTaskSubmit}
            onCancel={() => {
              setIsSubTaskDialogOpen(false);
              setEditingSubTask(null);
              setParentTaskId(null);
            }}
            isLoading={createSubTaskMutation.isPending || updateSubTaskMutation.isPending}
            isSubTask={true}
            projectEndDate={project?.end_date || undefined}
            onUpdateProjectEndDate={(newEndDate) => {
              updateProjectMutation.mutate({ end_date: newEndDate });
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Project Completion Dialog */}
      <AlertDialog open={showProjectCompletionDialog} onOpenChange={setShowProjectCompletionDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Incomplete Tasks and Subtasks
            </AlertDialogTitle>
            <AlertDialogDescription>
              This project has incomplete tasks and/or subtasks. Are you sure you want to mark this project as complete? 
              The incomplete tasks and subtasks will remain unchanged.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmProjectComplete}>
              Complete Project
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}