/**
 * API Service - Replaces Supabase client
 * Provides a centralized way to access our REST API
 */

import { ApiClient } from '../api-client/client';

class ApiService {
  private client: ApiClient;
  private token: string | null = null;

  constructor() {
    // Check for stored token on initialization (safely handle SSR)
    const storedToken = typeof window !== 'undefined' ? localStorage.getItem('access_token') : null;
    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1';
    this.token = storedToken;
    this.client = new ApiClient(baseUrl, storedToken || undefined);
  }

  setAuthToken(token: string | null) {
    this.token = token;
    // Recreate client with new token
    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1';
    this.client = new ApiClient(baseUrl, token || undefined);
  }

  getAuthToken() {
    return this.token;
  }

  // Auth methods
  auth = {
    signIn: async (email: string, password: string) => {
      const response = await this.client.signIn(email, password);
      if (response.data.accessToken) {
        this.setAuthToken(response.data.accessToken);
      }
      return response;
    },

    signInWithOAuth: async (provider: 'google', redirectTo?: string) => {
      return await this.client.signInWithOAuth(provider, redirectTo);
    },

    signOut: async () => {
      const response = await this.client.signOut();
      this.setAuthToken(null);
      return response;
    },

    getUser: async () => {
      return this.client.getCurrentUser();
    },

    getSession: async () => {
      // For compatibility with Supabase auth calls
      try {
        const userResponse = await this.client.getCurrentUser();
        return {
          data: {
            session: this.token ? {
              access_token: this.token,
              user: userResponse.data
            } : null
          },
          error: null
        };
      } catch (error: any) {
        // If it's a 401, this is expected - user is not logged in
        if (error.message?.includes('401')) {
          return {
            data: { session: null },
            error: null
          };
        }
        // For other errors, return them
        return {
          data: { session: null },
          error
        };
      }
    },

    onAuthStateChange: (callback: (event: string, session: any) => void) => {
      // For now, this is a no-op since we don't have real-time auth state
      // In a real implementation, you might use WebSockets or polling
      return {
        data: { 
          subscription: {
            unsubscribe: () => {
              // No-op for now
            }
          }
        }
      };
    }
  };

  // Database-like interface to match Supabase patterns
  from(table: string) {
    switch (table) {
      case 'companies':
        return {
          select: (columns = '*') => {
            // Create a promise-like object that supports both direct await and chaining
            const baseQuery = async () => {
              try {
                const response = await this.client.getCompanies();
                return { data: response.data || [], error: null };
              } catch (error) {
                return { data: [], error };
              }
            };
            
            // Add methods to the promise for chaining
            const selectPromise = baseQuery() as any;
            selectPromise.eq = (column: string, value: any) => ({
              single: async () => {
                try {
                  if (column === 'id') {
                    const response = await this.client.getCompany(value);
                    return { data: response.data, error: null };
                  } else if (column === 'is_deleted') {
                    const response = await this.client.getCompanies();
                    const filtered = response.data.filter((company: any) => company.is_deleted === value);
                    return { data: filtered[0] || null, error: null };
                  } else {
                    throw new Error(`Company query by ${column} not supported`);
                  }
                } catch (error) {
                  return { data: null, error };
                }
              },
              async execute() {
                try {
                  if (column === 'id') {
                    const response = await this.client.getCompany(value);
                    return { data: [response.data], error: null };
                  } else if (column === 'is_deleted') {
                    const response = await this.client.getCompanies();
                    const filtered = response.data.filter((company: any) => company.is_deleted === value);
                    return { data: filtered, error: null };
                  } else {
                    throw new Error(`Company query by ${column} not supported`);
                  }
                } catch (error) {
                  return { data: null, error };
                }
              }
            });
            
            return selectPromise;
          },
          insert: async (data: any) => {
            try {
              const response = await this.client.createCompany(data);
              return { data: response.data, error: null };
            } catch (error) {
              return { data: null, error };
            }
          },
          update: async (data: any) => {
            // This needs to be called with .eq('id', id) pattern
            return {
              eq: (column: string, value: string) => ({
                async execute() {
                  try {
                    const response = await this.client.updateCompany(value, data);
                    return { data: response.data, error: null };
                  } catch (error) {
                    return { data: null, error };
                  }
                }
              })
            };
          },
          delete: () => ({
            eq: (column: string, value: string) => ({
              async execute() {
                try {
                  await this.client.deleteCompany(value);
                  return { data: null, error: null };
                } catch (error) {
                  return { data: null, error };
                }
              }
            })
          })
        };

      case 'company_statuses':
        return {
          select: (columns = '*') => {
            // Create a promise-like object that supports both direct await and chaining
            const baseQuery = async () => {
              try {
                const response = await this.client.getCompanyStatuses();
                console.log('Company statuses API response:', response);
                
                // Deduplicate by name and pipeline_order (take the most recent one)
                const uniqueData = (response.data || []).reduce((acc: any[], item: any) => {
                  const existing = acc.find(existing => existing.name === item.name && existing.pipeline_order === item.pipeline_order);
                  if (!existing) {
                    acc.push(item);
                  } else {
                    // Replace with more recent one (later created_at)
                    if (new Date(item.created_at) > new Date(existing.created_at)) {
                      const index = acc.indexOf(existing);
                      acc[index] = item;
                    }
                  }
                  return acc;
                }, []);
                
                console.log('Deduplicated company statuses:', uniqueData);
                return { data: uniqueData, error: null };
              } catch (error) {
                console.error('Company statuses API error:', error);
                return { data: [], error };
              }
            };
            
            return baseQuery();
          }
        };

      case 'contacts':
        return {
          select: (columns = '*') => {
            // Create a promise-like object that supports both direct await and chaining
            const baseQuery = async () => {
              try {
                const response = await this.client.getContacts();
                return { data: response.data || [], error: null };
              } catch (error) {
                return { data: [], error };
              }
            };
            
            // Add methods to the promise for chaining
            const selectPromise = baseQuery() as any;
            selectPromise.eq = (column: string, value: any) => ({
              single: async () => {
                try {
                  if (column === 'id') {
                    const response = await this.client.getContact(value);
                    return { data: response.data, error: null };
                  } else if (column === 'is_deleted') {
                    const response = await this.client.getContacts();
                    const filtered = response.data.filter((contact: any) => contact.is_deleted === value);
                    return { data: filtered[0] || null, error: null };
                  } else {
                    throw new Error(`Contact query by ${column} not supported`);
                  }
                } catch (error) {
                  return { data: null, error };
                }
              },
              async execute() {
                try {
                  if (column === 'id') {
                    const response = await this.client.getContact(value);
                    return { data: [response.data], error: null };
                  } else if (column === 'is_deleted') {
                    const response = await this.client.getContacts();
                    const filtered = response.data.filter((contact: any) => contact.is_deleted === value);
                    return { data: filtered, error: null };
                  } else {
                    throw new Error(`Contact query by ${column} not supported`);
                  }
                } catch (error) {
                  return { data: null, error };
                }
              }
            });
            
            return selectPromise;
          },
          insert: async (data: any) => {
            try {
              const response = await this.client.createContact(data);
              return { data: response.data, error: null };
            } catch (error) {
              return { data: null, error };
            }
          },
          update: async (data: any) => {
            return {
              eq: (column: string, value: string) => ({
                async execute() {
                  try {
                    const response = await this.client.updateContact(value, data);
                    return { data: response.data, error: null };
                  } catch (error) {
                    return { data: null, error };
                  }
                }
              })
            };
          }
        };

      case 'deals':
        return {
          select: (columns = '*') => {
            // Create a promise-like object that supports both direct await and chaining
            const baseQuery = async () => {
              try {
                const response = await this.client.getDeals();
                return { data: response.data || [], error: null };
              } catch (error) {
                return { data: [], error };
              }
            };
            
            // Add methods to the promise for chaining
            const selectPromise = baseQuery() as any;
            selectPromise.eq = (column: string, value: any) => ({
              single: async () => {
                try {
                  if (column === 'id') {
                    const response = await this.client.getDeal(value);
                    return { data: response.data, error: null };
                  } else {
                    throw new Error(`Deal query by ${column} not supported`);
                  }
                } catch (error) {
                  return { data: null, error };
                }
              },
              async execute() {
                try {
                  if (column === 'id') {
                    const response = await this.client.getDeal(value);
                    return { data: [response.data], error: null };
                  } else {
                    throw new Error(`Deal query by ${column} not supported`);
                  }
                } catch (error) {
                  return { data: null, error };
                }
              }
            });
            
            return selectPromise;
          },
          insert: async (data: any) => {
            try {
              const response = await this.client.createDeal(data);
              return { data: response.data, error: null };
            } catch (error) {
              return { data: null, error };
            }
          },
          update: async (data: any) => {
            return {
              eq: (column: string, value: string) => ({
                async execute() {
                  try {
                    const response = await this.client.updateDeal(value, data);
                    return { data: response.data, error: null };
                  } catch (error) {
                    return { data: null, error };
                  }
                }
              })
            };
          }
        };

      case 'deal_stages':
        return {
          select: (columns = '*') => {
            // Create a promise-like object that supports both direct await and chaining
            const baseQuery = async () => {
              try {
                const response = await this.client.getDealStages();
                console.log('Deal stages API response:', response);
                
                // Deduplicate by name and pipeline_order (take the most recent one)
                const uniqueData = (response.data || []).reduce((acc: any[], item: any) => {
                  const existing = acc.find(existing => existing.name === item.name && existing.pipeline_order === item.pipeline_order);
                  if (!existing) {
                    acc.push(item);
                  } else {
                    // Replace with more recent one (later created_at)
                    if (new Date(item.created_at) > new Date(existing.created_at)) {
                      const index = acc.indexOf(existing);
                      acc[index] = item;
                    }
                  }
                  return acc;
                }, []);
                
                console.log('Deduplicated deal stages:', uniqueData);
                return { data: uniqueData, error: null };
              } catch (error) {
                return { data: [], error };
              }
            };
            
            return baseQuery();
          }
        };

      case 'interactions':
        return {
          select: (columns = '*') => {
            return {
              eq: (column: string, value: any) => ({
                order: (orderColumn: string, options?: any) => ({
                  async execute() {
                    try {
                      const response = await this.client.getInteractions();
                      let data = response.data.interactions || response.data;
                      // Filter by the eq condition
                      if (column === 'contact_id') {
                        data = data.filter((interaction: any) => interaction.contact_id === value);
                      } else if (column === 'entity_type') {
                        data = data.filter((interaction: any) => interaction.entity_type === value);
                      } else if (column === 'entity_id') {
                        data = data.filter((interaction: any) => interaction.entity_id === value);
                      }
                      // Apply ordering if specified
                      if (orderColumn && data.length > 0) {
                        data.sort((a: any, b: any) => {
                          const aVal = a[orderColumn];
                          const bVal = b[orderColumn];
                          if (options?.ascending === false) {
                            return bVal > aVal ? 1 : -1;
                          }
                          return aVal > bVal ? 1 : -1;
                        });
                      }
                      return { data, error: null };
                    } catch (error) {
                      return { data: null, error };
                    }
                  }
                }),
                async execute() {
                  try {
                    const response = await this.client.getInteractions();
                    let data = response.data.interactions || response.data;
                    // Filter by the eq condition
                    if (column === 'contact_id') {
                      data = data.filter((interaction: any) => interaction.contact_id === value);
                    } else if (column === 'entity_type') {
                      data = data.filter((interaction: any) => interaction.entity_type === value);
                    } else if (column === 'entity_id') {
                      data = data.filter((interaction: any) => interaction.entity_id === value);
                    }
                    return { data, error: null };
                  } catch (error) {
                    return { data: null, error };
                  }
                }
              }),
              async execute() {
                try {
                  const response = await this.client.getInteractions();
                  return { data: response.data.interactions || response.data, error: null };
                } catch (error) {
                  return { data: null, error };
                }
              }
            };
          },
          insert: async (data: any) => {
            try {
              const response = await this.client.createInteraction(data);
              return { data: response.data, error: null };
            } catch (error) {
              return { data: null, error };
            }
          }
        };

      case 'documents':
        return {
          select: (columns = '*') => {
            return {
              eq: (column: string, value: any) => ({
                async execute() {
                  try {
                    const response = await this.client.getDocuments();
                    let data = response.data.documents || response.data;
                    // Filter by metadata properties
                    if (column === 'metadata->>sourceId') {
                      data = data.filter((doc: any) => doc.metadata?.sourceId === value);
                    }
                    return { data, error: null };
                  } catch (error) {
                    return { data: null, error };
                  }
                }
              }),
              async execute() {
                try {
                  const response = await this.client.getDocuments();
                  return { data: response.data.documents || response.data, error: null };
                } catch (error) {
                  return { data: null, error };
                }
              }
            };
          },
          insert: async (data: any) => {
            try {
              const response = await this.client.createDocument(data);
              return { data: response.data, error: null };
            } catch (error) {
              return { data: null, error };
            }
          }
        };

      case 'filter_tags':
        return {
          select: (columns = '*') => {
            return {
              eq: (column: string, value: any) => ({
                async execute() {
                  try {
                    const response = await this.client.getFilterTags();
                    let data = response.data;
                    if (column === 'id') {
                      data = data.filter((tag: any) => tag.id === value);
                    }
                    return { data, error: null };
                  } catch (error) {
                    return { data: null, error };
                  }
                }
              }),
              async execute() {
                try {
                  const response = await this.client.getFilterTags();
                  return { data: response.data, error: null };
                } catch (error) {
                  return { data: null, error };
                }
              }
            };
          },
          insert: async (data: any) => {
            try {
              const response = await this.client.createFilterTag(data);
              return { data: response.data, error: null };
            } catch (error) {
              return { data: null, error };
            }
          }
        };

      case 'research_notes_with_entities':
        return {
          select: (columns = '*') => {
            return {
              eq: (column: string, value: any) => ({
                order: (orderColumn: string, options?: any) => ({
                  async execute() {
                    try {
                      if (column === 'entity_type' && value === 'company') {
                        // Get the entity_id from a second eq call
                        return {
                          eq: (column2: string, value2: any) => ({
                            order: (orderColumn: string, options?: any) => ({
                              async execute() {
                                try {
                                  if (column2 === 'entity_id') {
                                    const response = await this.client.getCompanyResearchNotes(value2);
                                    let data = response.data || [];
                                    // Map backend structure to frontend expected structure
                                    data = data.map((note: any) => ({
                                      note_id: note.id,
                                      entity_type: note.entity_type,
                                      entity_id: note.entity_id,
                                      content_markdown: note.note_content,
                                      created_at: note.created_at,
                                      title: note.title || 'Research Note', // Backend doesn't have title yet
                                      created_by_name: note.created_by_name || null
                                    }));
                                    // Apply ordering if specified
                                    if (orderColumn && data.length > 0) {
                                      data.sort((a: any, b: any) => {
                                        const aVal = a[orderColumn];
                                        const bVal = b[orderColumn];
                                        if (options?.ascending === false) {
                                          return bVal > aVal ? 1 : -1;
                                        }
                                        return aVal > bVal ? 1 : -1;
                                      });
                                    }
                                    return { data, error: null };
                                  } else {
                                    throw new Error(`Research notes query by ${column2} not supported`);
                                  }
                                } catch (error) {
                                  return { data: [], error };
                                }
                              }
                            })
                          })
                        };
                      } else {
                        throw new Error(`Research notes query by ${column} = ${value} not supported`);
                      }
                    } catch (error) {
                      return { data: [], error };
                    }
                  }
                })
              })
            };
          }
        };

      case 'profiles':
        return {
          select: (columns = '*') => {
            const selectQuery = {
              eq: (column: string, value: string) => ({
                single: async () => {
                  try {
                    // For profiles, we'll use the current user endpoint since it returns profile data
                    if (column === 'id') {
                      const response = await this.client.getCurrentUser();
                      // Map user data to profile format expected by the UI
                      const profileData = {
                        id: response.data.id,
                        full_name: response.data.full_name || null,
                        avatar_url: response.data.avatar_url || null,
                        email: response.data.email,
                        created_at: response.data.created_at,
                        updated_at: response.data.updated_at
                      };
                      return { data: profileData, error: null };
                    } else {
                      throw new Error(`Profile query by ${column} not supported`);
                    }
                  } catch (error) {
                    return { data: null, error };
                  }
                },
                async execute() {
                  try {
                    if (column === 'id') {
                      const response = await this.client.getCurrentUser();
                      const profileData = {
                        id: response.data.id,
                        full_name: response.data.full_name || null,
                        avatar_url: response.data.avatar_url || null,
                        email: response.data.email,
                        created_at: response.data.created_at,
                        updated_at: response.data.updated_at
                      };
                      return { data: [profileData], error: null };
                    } else {
                      throw new Error(`Profile query by ${column} not supported`);
                    }
                  } catch (error) {
                    return { data: null, error };
                  }
                }
              })
            };
            return selectQuery;
          }
        };

      default:
        console.warn(`Table "${table}" not implemented in API service`);
        return {
          select: async () => ({ data: [], error: new Error(`Table ${table} not implemented`) }),
          insert: async () => ({ data: null, error: new Error(`Table ${table} not implemented`) }),
          update: async () => ({ data: null, error: new Error(`Table ${table} not implemented`) }),
          delete: async () => ({ data: null, error: new Error(`Table ${table} not implemented`) })
        };
    }
  }
}

// Create a singleton instance
export const apiService = new ApiService();

// Export the instance as default to match Supabase patterns
export default apiService;