# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a multi-language monorepo using <PERSON><PERSON> as the build system. It contains services and shared libraries in Python, Go, and Java, all containerized with Docker.

## Software Versions

### Core Build Tools
- **Bazel**: 8.3.1 (via Bazelisk 1.26.0)
- **Bzlmod**: Enabled (modern Bazel module system)

### Languages & Runtimes
- **Node.js**: 24.3.0
- **npm**: 11.4.2
- **Python**: 3.12+ (configured), 3.9.6 (system)
- **Go**: 1.24 (configured in go.mod)
- **Java**: 21 (remote JDK via <PERSON><PERSON>)

### Frontend Stack (CRM Web App)
- **React**: 18.3.1
- **TypeScript**: 5.5.3
- **Vite**: 5.4.1
- **Tailwind CSS**: 3.4.11
- **React Query**: 5.56.2
- **React Router**: 6.26.2
- **Radix UI**: 1.1.x-1.2.x (various components)

### Backend Dependencies
- **FastAPI**: 0.104.0+ (Python)
- **Gin**: 1.9.1 (Go)
- **PostgreSQL**: 17 (Docker)
- **Supabase**: 2.49.3 (legacy/migration)

### Development Tools
- **ESLint**: 9.9.0
- **Prettier**: Configured
- **Lovable Tagger**: 1.1.7
- **OpenAPI Generator**: 7.2.0 (for API client generation)

### Version Compatibility Notes
- **Bazel 8.3.1**: Uses Bzlmod (modern module system), WORKSPACE deprecated
- **Node.js 24.3.0**: Latest LTS features supported
- **React 18.3.1**: Concurrent features enabled
- **TypeScript 5.5.3**: Latest strict type checking
- **Python 3.12+**: Modern Python features required

### Version Management
- **Bazel**: Managed via Bazelisk for automatic version switching
- **Node.js**: Use nvm or system package manager
- **Python**: Dependencies managed via pyproject.toml and UV
- **Go**: Dependencies managed via go.mod
- **Java**: Remote JDK managed via Bazel

## API-First Development Guidelines

### OpenAPI Specification Requirements

**CRITICAL: Always use OpenAPI specifications for web development**

When working on frontend components in `platform/web/`, follow these strict guidelines:

1. **OpenAPI First**: All API communication MUST use the generated TypeScript client from OpenAPI specifications
2. **Create Missing Specs**: If an API endpoint doesn't have an OpenAPI specification, create one before implementing frontend features
3. **No Custom Types**: Never create custom type definitions that duplicate API models - always import from `@/api-client/src/models`
4. **Generated Client Only**: Use the generated API client hooks, not direct database queries or custom fetch calls
5. **Consistent Naming**: Generated types use camelCase - ensure all component property access matches this

### Type Usage Rules

```typescript
// ✅ CORRECT - Use generated types
import type { Company, Deal, Contact, CompanyStatus, DealStage } from '@/api-client/src/models';

// ❌ FORBIDDEN - Don't create duplicate types
type Company = { id: string; name: string; };
interface Deal { title: string; amount: number; };
```

### API Client Usage Rules

```typescript
// ✅ CORRECT - Use generated API client
import { useCompaniesApi, useDealsApi } from '@/hooks/useApiClient';

const companiesApi = useCompaniesApi();
const response = await companiesApi.getCompanies();

// ❌ FORBIDDEN - Don't bypass API client
const { data } = await supabase.from('companies').select('*');
const response = await fetch('/api/companies');
```

### Regenerating API Client

```bash
# When OpenAPI specs change, regenerate the client
bazel run //platform/web:generate_client
```

## Architecture

### CRM Application Flow
The CRM application follows a modern microservices architecture with the following request flow:

**UI → Gateway → Backend Services → Database**

1. **CRM Web App** (React/TypeScript) on port 8080 - `platform/web/`
   - Frontend application built with React 18.3.1 and TypeScript
   - Makes API calls to `http://localhost:8085/api/v1` (Gateway)
   - Handles user authentication and session management

2. **Gateway** (Go/Gin) on port 8085 - `gateway/`
   - Central API gateway that routes requests to appropriate services
   - Handles CORS, logging, and request proxying
   - Routes `/api/v1/auth/*` to Auth Service
   - Routes `/api/v1/*` to CRM Backend

3. **Auth Service** (Go/Gin) on port 8004 - `platform/auth/`
   - Handles user authentication (signin, signout, session management)
   - JWT token validation and user management
   - Provides endpoints: `/api/v1/auth/signin`, `/api/v1/auth/session`, etc.

4. **CRM Backend** (Go/Gin) on port 8003 - `platform/crm_backend/`
   - Core business logic for CRM operations
   - Manages companies, contacts, deals, and user data
   - Direct database access for all CRM operations

5. **PostgreSQL Database** on port 5432 - `platform/db/`
   - Persistent storage for all application data
   - Managed via Flyway migrations

### Example Services (Legacy)
- **example_python** - FastAPI service on port 8000
- **example_go** - Gin web service on port 8001  
- **example_java** - Spring Boot service on port 8002

### Shared Libraries
- **python_lib** - Common Python utilities
- **go_lib** - Common Go utilities (includes logging middleware)
- **java_lib** - Common Java utilities

## Build System

This project uses **Bazel** for build management with language-specific rules:
- `@rules_python` for Python
- `@io_bazel_rules_go` for Go
- `@rules_java` for Java
- `@rules_docker` for container images

## Dependency Management

### Python
- **UV** for package management (configured in `pyproject.toml`)
- Python 3.12 is the standard version

### Go  
- **go.mod** in the root manages Go dependencies
- Go 1.21 is the standard version

### Java
- **Maven** for dependency management in Java services
- Java 24 is the standard version

## Common Commands

### Development Setup (Docker-based)

#### Quick Start with Bazel (Recommended):
```bash
# 🚀 ONE COMMAND SETUP: Start development environment (database + gateway)
bazel run :start_dev_environment

# Check status of all services
bazel run :status_all_services

# Stop development environment
bazel run :stop_dev_environment
```

#### Manual Docker Compose (Alternative):
```bash
# Start all services in development mode
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# Start only database and gateway for local development
docker-compose -f docker-compose.dev.yml up postgres gateway

# Then run services locally with Bazel:
bazel run //services/auth:auth_service          # Port 8004
bazel run //apps/crm/backend:crm_backend        # Port 8005  
bazel run //apps/project-hub/backend:project_hub_backend  # Port 8003
bazel run //apps/crm/web:dev                    # Port 8081
bazel run //apps/project-hub/frontend:dev       # Port 8082
```

#### Production Docker Setup:
```bash
# Build and start all services
docker-compose up --build

# Access the application
open http://localhost:8080
```

### Build all services:
```bash
bazel build //...
```

### Build specific service:
```bash
# Platform (includes all CRM services)
bazel build //platform

# CRM Services
bazel build //gateway:gateway
bazel build //platform/auth:auth_service
bazel build //platform/crm_backend:crm_backend

# Example Services
bazel build //example_python:example_python
bazel build //example_go:example_go  
bazel build //example_java:example_java
```

### Build Docker images:
```bash
# Platform backend services (recommended for production)
bazel build //platform/gateway:gateway_image
bazel build //platform/auth:auth_service_image  
bazel build //platform/crm_backend:crm_backend_image

# Build all backend services at once
bazel build //platform/gateway:gateway_image //platform/auth:auth_service_image //platform/crm_backend:crm_backend_image

# Example services
bazel build //example_python:example_python_image
bazel build //example_go:example_go_image
bazel build //example_java:example_java_image
```

### Deploy Docker images to dev environment:
```bash
# 🚀 RECOMMENDED: Automated build and push to dev using Terraform configuration
# This command automatically handles project ID, registry URL, authentication, and deployment
bazel run //terraform:build_and_push_backend_dev
```

### Database operations:
```bash
# Start PostgreSQL database
bazel run //platform/db:start

# Stop PostgreSQL database
bazel run //platform/db:stop

# Run database migrations
bazel run //platform/db:migrate

# Check database status
bazel run //platform/db:status
```

### Run services locally:

To run a service use:
```bash
bazel run //service:service
```
Unless otherwise prompted USE A TIMEOUT SO THAT IF THE SERVICE RUNS IT DOESN'T BLOCK YOUR THINKING. 

#### CRM Application Services:
```bash
# 1. Start database first
bazel run //platform/db:start

# 2. Run CRM services in order
bazel run //platform/auth:auth_service
bazel run //platform/crm_backend:crm_backend  
bazel run //gateway:gateway

# 3. Run frontend
bazel run //platform/web:dev
```

#### Example Services:
```bash
# Python service
cd example_python && python main.py

# Go service  
cd example_go && go run main.go

# Java service
cd example_java && mvn spring-boot:run
```

### Docker commands:
```bash
# Build and run Python service
docker build -f example_python/Dockerfile -t example-python .
docker run -p 8000:8000 example-python

# Build and run Go service
docker build -f example_go/Dockerfile -t example-go .
docker run -p 8001:8001 example-go

# Build and run Java service  
docker build -f example_java/Dockerfile -t example-java .
docker run -p 8002:8002 example-java
```

## Database Setup

The project includes a PostgreSQL database with Flyway migrations:

- **PostgreSQL 17** running in Docker
- **Flyway** for database schema management
- Compatible with local development and GCP Cloud SQL
- Initial schema includes a `users` table with UUID primary keys

### Database Workflow:
1. Start database: `bazel run //platform/db:start`
2. Run migrations: `bazel run //platform/db:migrate`
3. Develop with database running
4. Stop database: `bazel run //platform/db:stop`

## Development Workflow

1. Make changes to services or shared libraries
2. Use Bazel to build and test
3. Build Docker images for deployment
4. All shared libraries are automatically included in service builds

## Project Structure

```
├── WORKSPACE                 # Bazel workspace configuration
├── BUILD.bazel              # Root build file  
├── pyproject.toml           # Python dependencies (UV)
├── go.mod                   # Go dependencies
├── ports.conf               # Service port configuration
├── platform/                # Platform directory containing CRM services
│   ├── BUILD.bazel          # Platform build configuration
│   ├── gateway/             # API Gateway service (Go/Gin)
│   │   ├── config/          # Gateway configuration
│   │   ├── handlers/        # HTTP handlers
│   │   ├── middleware/      # CORS and logging middleware
│   │   ├── router/          # Route definitions
│   │   └── BUILD.bazel      # Gateway build configuration
│   ├── web/                 # React/TypeScript frontend application
│   │   ├── src/             # React source code
│   │   ├── api-client/      # Generated API client
│   │   └── BUILD.bazel      # Frontend build configuration
│   ├── auth/                # Authentication service (Go/Gin)
│   │   ├── config/          # Auth service configuration
│   │   ├── handlers/        # Auth handlers (signin, signout, etc.)
│   │   ├── middleware/      # Auth middleware
│   │   └── BUILD.bazel      # Auth service build configuration
│   ├── crm_backend/         # CRM Backend service (Go/Gin)
│   │   ├── config/          # Backend configuration
│   │   ├── database/        # Database connection and migrations
│   │   ├── handlers/        # Business logic handlers
│   │   ├── middleware/      # Auth and CORS middleware
│   │   └── BUILD.bazel      # Backend build configuration
│   ├── db/                  # PostgreSQL database setup
│   │   ├── migrations/      # Flyway database migrations
│   │   ├── scripts/         # Database management scripts
│   │   └── BUILD.bazel      # Database build configuration
│   ├── api-clients/         # API clients for different languages
│   │   ├── python/          # Python API client
│   │   ├── go/              # Go API client
│   │   ├── java/            # Java API client
│   │   └── BUILD.bazel      # API clients build configuration
│   └── rest-api/            # OpenAPI specifications
│       ├── openapi.yaml     # Main API specification
│       ├── *.yaml           # Service-specific API specs
│       └── BUILD.bazel      # API specification build configuration
├── example_python/          # Example Python FastAPI service
├── example_go/              # Example Go Gin service
├── example_java/            # Example Java Spring Boot service  
├── python_lib/              # Shared Python utilities
├── go_lib/                  # Shared Go utilities (logging, etc.)
├── java_lib/                # Shared Java utilities
└── logs/                    # Claude Code tool usage logs
```

# important-instruction-reminders

## Web Development - OpenAPI First

When working on frontend components in `platform/web/`, ALWAYS:

1. **Use OpenAPI generated types**: Import from `@/api-client/src/models` - NEVER create custom types that duplicate API models
2. **Use generated API client**: Use hooks from `@/hooks/useApiClient` - NEVER use direct Supabase queries or custom fetch calls
3. **Create missing OpenAPI specs**: If an endpoint lacks OpenAPI specification, create one BEFORE implementing frontend features
4. **Follow camelCase naming**: Generated types use camelCase - ensure all property access matches this pattern
5. **Regenerate on spec changes**: Run `bazel run //platform/web:generate_client` when OpenAPI specs are updated

**These rules are non-negotiable for web development - they ensure type safety, consistency, and maintainability.**
