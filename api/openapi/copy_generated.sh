#!/bin/bash

# Copy generated Go files to source directory for easy access
# This script copies the generated files from bazel-bin to the source tree

set -euo pipefail

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Create the target directory
mkdir -p "$SCRIPT_DIR/generated/go"

# Copy the generated file
if [ -f "$SCRIPT_DIR/../../bazel-bin/platform/rest-api/gen.go" ]; then
    cp "$SCRIPT_DIR/../../bazel-bin/platform/rest-api/gen.go" "$SCRIPT_DIR/generated/go/"
    echo "✅ Copied generated Go file to $SCRIPT_DIR/generated/go/"
else
    echo "❌ Generated file not found. Run 'bazel build //platform/rest-api:generate_go_code' first"
    exit 1
fi