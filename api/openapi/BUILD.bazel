load("@rules_go//go:def.bzl", "go_library")

package(default_visibility = ["//visibility:public"])

# OpenAPI specification files
exports_files([
    "openapi.yaml",
    "auth.yaml",
    "companies.yaml",
    "contacts.yaml",
    "deals.yaml",
    "interactions.yaml",
    "arli.yaml",
])

# Validate OpenAPI spec using <PERSON>oc<PERSON>
genrule(
    name = "validate_spec",
    srcs = ["openapi.yaml"],
    outs = ["validation_result.txt"],
    cmd = """
        redocly lint $(location openapi.yaml) > $@ 2>&1 || echo "Validation completed with warnings" > $@
    """,
)

# Generate Go code using oapi-codegen (run manually to update files)
# bazel run //api/openapi:generate_go_server
sh_binary(
    name = "generate_go_server",
    srcs = ["generate.sh"],
    data = [
        "oapi-codegen.yaml",
        "openapi.yaml",
        "tools.go",
    ],
)

# Go library for generated server code
go_library(
    name = "openapi_server",
    srcs = [":generate_go_code"],
    importpath = "github.com/TwoDotAi/orbit/api/openapi/generated/go",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_oapi_codegen_runtime//:runtime",
        "@com_github_oapi_codegen_runtime//types",
    ],
)

# All generated Go files
filegroup(
    name = "generated_go_files",
    srcs = glob(
        ["generated/go/*.go"],
        allow_empty = True,
    ),
    visibility = ["//visibility:public"],
)

# Generate TypeScript client using openapi-typescript
genrule(
    name = "typescript_client",
    srcs = ["openapi-bundled.yaml"],
    outs = ["clients/typescript/src/index.ts"],
    cmd = """
        mkdir -p $$(dirname $@)
        # Use openapi-typescript for better TypeScript generation
        npx openapi-typescript $(location openapi-bundled.yaml) --output $@
    """,
)

# Generate Go server code using oapi-codegen (automated)
genrule(
    name = "generate_go_code",
    srcs = [
        "openapi-bundled.yaml",
        "oapi-codegen.yaml",
        "tools.go",
    ],
    outs = ["gen.go"],
    cmd = """
        # Set up Go environment
        export GOPATH=$$(mktemp -d)
        export GOMODCACHE=$$GOPATH/pkg/mod
        export GOCACHE=$$GOPATH/cache
        
        # Use the pre-bundled OpenAPI spec (no external references)
        cp $(location openapi-bundled.yaml) openapi-bundled.yaml
        
        # Generate the Go code using oapi-codegen
        # Install oapi-codegen if not available
        go install github.com/oapi-codegen/oapi-codegen/v2/cmd/oapi-codegen@latest
        
        # Generate the Go code directly to the output location
        $$GOPATH/bin/oapi-codegen -generate gin,types -package openapi openapi-bundled.yaml > $(location gen.go)
    """,
)

# Copy generated Go files to source directory
sh_binary(
    name = "copy_generated_files",
    srcs = ["copy_generated.sh"],
    data = [":generate_go_code"],
)

# All clients target
filegroup(
    name = "all_clients",
    srcs = [
        ":generate_go_code",
        ":typescript_client",
        ":validate_spec",
    ],
)

# Clean target to remove generated files
sh_binary(
    name = "clean",
    srcs = ["clean.sh"],
)

go_library(
    name = "rest-api",
    srcs = ["gen.go"],
    importpath = "github.com/TwoDotAi/orbit/api/openapi",
    deps = [
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_oapi_codegen_runtime//:runtime",
        "@com_github_oapi_codegen_runtime//types",
    ],
)
