# CRM Monorepo - Bazel Build System

Multi-language monorepo for CRM system with internal agents using Bazel build system.

## Getting Started

### Prerequisites

#### Required Software

- **Bazelisk** (Bazel version manager)
- **Node.js** 24.3.0+ and **npm** 11.4.2+
- **Python** 3.12+ (for Python services)
- **Go** 1.21+ (for Go services)
- **Docker** (for containerization)
- **Redocly CLI** (for OpenAPI bundling)

#### Installation

```bash
# Install Bazelisk (Bazel version manager)
# On macOS
brew install bazelisk

# On Windows 
winget install Bazel.Bazelisk 
choco install bazelisk  
scoop install bazelisk

# Install Go (required for Go services and oapi-codegen)
# On macOS
brew install go

# On Windows
winget install GoLang.Go
choco install golang
scoop install go

# Install Node.js (if not already installed)
# On macOS
brew install node

# On Windows
winget install OpenJS.NodeJS

# Install Redocly CLI (required for OpenAPI bundling)
npm install -g @redocly/cli
```

### Software Versions

This project uses the following software versions:

#### Core Build System
- **Bazel**: 8.3.1 (via Bazelisk 1.26.0)
- **Bzlmod**: Enabled (modern Bazel module system)

#### Languages & Runtimes
- **Node.js**: 24.3.0
- **npm**: 11.4.2
- **Python**: 3.12+ (configured), 3.9.6+ (minimum)
- **Go**: 1.21 (configured in go.mod)
- **Java**: 21 (remote JDK via Bazel)

#### Frontend Stack (CRM Web App)
- **React**: 18.3.1
- **TypeScript**: 5.5.3
- **Vite**: 5.4.1
- **Tailwind CSS**: 3.4.11
- **React Query**: 5.56.2
- **React Router**: 6.26.2

#### Backend Dependencies
- **FastAPI**: 0.104.0+ (Python)
- **Gin**: 1.9.1 (Go)
- **PostgreSQL**: 17 (Docker)

#### Development Tools
- **ESLint**: 9.9.0
- **OpenAPI Generator**: 7.2.0
- **Lovable Tagger**: 1.1.7

### Quick Start with Make

```bash
# Setup development environment
make dev-setup

# Build all services
make build

# Run all tests
make test

# Run database tests
make db-test

# Build and run CRM backend
make crm-backend
make run-crm-backend

# Teardown development environment
make dev-teardown
```

### Direct Bazel Commands

```bash
# Build everything
bazel build //...

# Build specific services
bazel build //crm_backend:crm_backend
bazel build //example_python:example_python
bazel build //example_go:example_go  
bazel build //example_java:example_java

# Run services
bazel run //crm_backend:crm_backend
bazel run //example_python:example_python
bazel run //example_go:example_go
bazel run //example_java:example_java

# Run tests
bazel test //...
bazel test //db/tests:database_test_suite
```

## API Client Generation

The project includes a comprehensive API client generation system that creates type-safe clients from OpenAPI specifications.

### Quick Start

```bash
# Generate all API clients
./scripts/generate-all-clients.sh

# Generate only TypeScript client for CRM web app
./platform/rest-api/generate-simple-client.sh

# Build CRM web app with fresh API client
cd platform/web && npm run build:with-client
```

### Available Clients

- **TypeScript** - For React web applications (`platform/web/src/api-client/`)
- **Python** - For Python services (coming soon)
- **Go** - For Go services (coming soon)
- **Java** - For Java services (coming soon)

### API Documentation

- **OpenAPI Spec**: `platform/rest-api/openapi.yaml`
- **Individual Specs**: `platform/rest-api/{auth,companies,contacts,deals,interactions,arli}.yaml`
- **Client Documentation**: `platform/web/src/api-client/README.md`

### Development Workflow

1. **Update API Specification**: Modify `platform/rest-api/openapi.yaml`
2. **Generate Clients**: Run `./scripts/generate-all-clients.sh`
3. **Update Applications**: Use generated clients in your code
4. **Test Integration**: Verify API calls work correctly

## Project Structure

```
├── MODULE.bazel              # Bzlmod dependency management
├── BUILD.bazel               # Root build file  
├── .bazelrc                  # Bazel configuration
├── requirements.txt          # Python dependencies
├── go.mod                    # Go dependencies
├── example_python/           # Python service
├── example_go/               # Go service
├── example_java/             # Java service  
├── python_lib/               # Shared Python utilities
├── go_lib/                   # Shared Go utilities
├── java_lib/                 # Shared Java utilities
└── logs/                     # Build and runtime logs
```

## Adding New Microservices

### Adding a Python Service

1. **Create service directory:**
   ```bash
   mkdir my_new_python_service
   cd my_new_python_service
   ```

2. **Create main.py:**
   ```python
   from python_lib.utils import get_greeting

   def main():
       print("My New Python Service starting...")
       print(f"Message: {get_greeting('My New Python Service')}")
       print("Status: healthy")
       
       # Add your service logic here
       print("Service would be running on port 8003")

   if __name__ == "__main__":
       main()
   ```

3. **Create BUILD.bazel:**
   ```python
   load("@rules_python//python:defs.bzl", "py_binary")

   py_binary(
       name = "my_new_python_service",
       srcs = ["main.py"],
       main = "main.py",
       deps = [
           "//python_lib:python_lib",
           # Add external dependencies here when needed:
           # "@pip//fastapi",
           # "@pip//uvicorn", 
       ],
   )
   ```

4. **Test the build:**
   ```bash
   bazel build //my_new_python_service:my_new_python_service
   bazel run //my_new_python_service:my_new_python_service
   ```

### Adding a Go Service

1. **Create service directory:**
   ```bash
   mkdir my_new_go_service
   cd my_new_go_service
   ```

2. **Create main.go:**
   ```go
   package main

   import (
       "encoding/json"
       "fmt"
       "log"
       "net/http"

       "github.com/TwoDotAi/mono/go_lib/utils"
   )

   func main() {
       http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
           w.Header().Set("Content-Type", "application/json")
           response := map[string]string{
               "message": utils.GetGreeting("My New Go Service"),
           }
           json.NewEncoder(w).Encode(response)
       })

       http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
           w.Header().Set("Content-Type", "application/json")
           response := map[string]string{
               "status":  "healthy",
               "service": "my_new_go_service",
           }
           json.NewEncoder(w).Encode(response)
       })

       fmt.Println("My New Go Service starting on :8004")
       log.Fatal(http.ListenAndServe(":8004", nil))
   }
   ```

3. **Create BUILD.bazel:**
   ```python
   load("@rules_go//go:def.bzl", "go_binary")

   go_binary(
       name = "my_new_go_service",
       srcs = ["main.go"],
       deps = [
           "//go_lib/utils",
           # Add external dependencies here when needed:
           # "@com_github_gin_gonic_gin//:gin",
       ],
   )
   ```

4. **Test the build:**
   ```bash
   bazel build //my_new_go_service:my_new_go_service
   bazel run //my_new_go_service:my_new_go_service
   ```

### Adding a Java Service

1. **Create service directory structure:**
   ```bash
   mkdir -p my_new_java_service/src/main/java/com/agentrunner
   cd my_new_java_service
   ```

2. **Create MyNewJavaApplication.java:**
   ```java
   package com.agentrunner;

   import com.agentrunner.lib.Utils;

   public class MyNewJavaApplication {

       public static void main(String[] args) {
           System.out.println("My New Java Service starting...");
           System.out.println("Message: " + Utils.getGreeting("My New Java Service"));
           System.out.println("Status: healthy");
           System.out.println("Service: my_new_java_service");
           
           // Add your service logic here
           System.out.println("Service would be running on port 8005");
       }
   }
   ```

3. **Create BUILD.bazel:**
   ```python
   java_library(
       name = "my_new_java_service_lib",
       srcs = glob(["src/main/java/**/*.java"]),
       deps = [
           "//java_lib:java_lib",
           # Add external dependencies here when needed:
           # "@maven//:org_springframework_boot_spring_boot_starter_web",
       ],
   )

   java_binary(
       name = "my_new_java_service",
       main_class = "com.agentrunner.MyNewJavaApplication",
       runtime_deps = [":my_new_java_service_lib"],
   )
   ```

4. **Test the build:**
   ```bash
   bazel build //my_new_java_service:my_new_java_service
   bazel run //my_new_java_service:my_new_java_service
   ```

## Adding External Dependencies

### Python Dependencies
1. Add to `requirements.txt`:
   ```
   fastapi==0.68.0
   uvicorn==0.15.0
   ```

2. Uncomment pip configuration in `MODULE.bazel`:
   ```python
   pip = use_extension("@rules_python//python/extensions:pip.bzl", "pip")
   pip.parse(
       hub_name = "pip",
       python_version = "3.12", 
       requirements_lock = "//:requirements.txt",
   )
   use_repo(pip, "pip")
   ```

3. Use in BUILD.bazel:
   ```python
   deps = [
       "@pip//fastapi",
       "@pip//uvicorn",
   ]
   ```

### Go Dependencies
1. Add to `go.mod`:
   ```
   require github.com/gin-gonic/gin v1.9.1
   ```

2. Generate `go.sum`:
   ```bash
   go mod tidy
   ```

3. Uncomment Go deps in `MODULE.bazel`:
   ```python
   go_deps = use_extension("@gazelle//:extensions.bzl", "go_deps")
   go_deps.from_file(go_mod = "//:go.mod")
   use_repo(go_deps, "com_github_gin_gonic_gin")
   ```

4. Use in BUILD.bazel:
   ```python
   deps = [
       "@com_github_gin_gonic_gin//:gin",
   ]
   ```

### Java Dependencies
1. Add to Maven configuration in `MODULE.bazel`:
   ```python
   maven.install(
       artifacts = [
           "org.springframework.boot:spring-boot-starter-web:3.1.0",
       ],
       repositories = [
           "https://repo1.maven.org/maven2",
       ],
   )
   ```

2. Use in BUILD.bazel:
   ```python
   deps = [
       "@maven//:org_springframework_boot_spring_boot_starter_web",
   ]
   ```

## Docker Support

To add Docker images for your services, uncomment the Docker rules in `WORKSPACE` and add image targets to your BUILD.bazel files:

```python
load("@rules_docker//python:image.bzl", "py_image")

py_image(
    name = "my_service_image",
    srcs = ["main.py"],
    main = "main.py",
    deps = [":my_service_deps"],
)
```

## Development Workflow

1. Make changes to services or shared libraries
2. Build and test: `bazel build //... && bazel test //...`
3. Run specific service: `bazel run //my_service:my_service`
4. Build Docker images: `bazel build //my_service:my_service_image`
5. All shared libraries are automatically included in service builds

## Deployment to GCP

The project includes Terraform-based deployment to Google Cloud Platform. Deploy the web application to the dev environment:

### Quick Deployment

```bash
# Complete build and deploy to dev environment
bazel run //terraform:deploy_web_dev
```

This command will:
- ✅ Build the React web application with Vite
- ✅ Generate API client from OpenAPI specifications  
- ✅ Deploy to GCS bucket with optimized cache headers
- ✅ Provide access URLs and health check endpoints

### Alternative Deployment Commands

```bash
# Deploy only (if already built)
bazel run //terraform:deploy_only_web_dev

# Build web app for GCP deployment
bazel run //platform/web:build_dist_complete

# Deploy using platform web scripts
bazel run //platform/web:deploy_gcp
```

### Access Deployed Application

After deployment, access your application via:
- **Load Balancer**: Check Terraform output for the HTTP URL
- **Health Check**: `https://storage.googleapis.com/<bucket>/health.json`  
- **Build Info**: `https://storage.googleapis.com/<bucket>/deployment-info.json`

## Development Setup

### Zero to Hero - Complete Development Environment Setup

For the fastest and most reliable development environment setup, use the **Zero2Hero script**. This script provides a complete end-to-end development environment setup in a single command.

#### Quick Start

```bash
# Complete development environment setup (recommended)
./scripts/zero2hero.sh
```

This single command will:
- ✅ **Clean Environment**: Stop any existing services and containers
- ✅ **Build Services**: Build all platform services with Bazel
- ✅ **Start Infrastructure**: Launch PostgreSQL database and nginx gateway in Docker
- ✅ **Seed Database**: Import comprehensive test data (users, companies, projects)
- ✅ **Start Backend Services**: Launch Auth, CRM, and ProjectHub backend services
- ✅ **Start Frontend Services**: Launch CRM and ProjectHub web applications
- ✅ **Validate Setup**: Test authentication, API endpoints, and data availability
- ✅ **Show Status**: Display comprehensive status dashboard with access URLs

#### What You Get

After running `./scripts/zero2hero.sh`, you'll have a fully operational development environment:

**🔧 Infrastructure Services (Docker):**
- PostgreSQL Database: `localhost:5432`
- nginx Gateway: `localhost:8080`

**⚙️ Backend Services (Local):**
- Auth Service: `localhost:8004`
- CRM Backend: `localhost:8005`
- ProjectHub Backend: `localhost:8003`

**🎨 Frontend Applications:**
- CRM Web App: `http://localhost:8080/crm/`
- ProjectHub: `http://localhost:8080/hub/`

**🗄️ Database Content:**
- 3 test users with working authentication
- 5 sample companies for CRM functionality
- 10 projects for ProjectHub functionality

#### Test Credentials

```bash
# Primary test account
Email: <EMAIL>
Password: TestPassword

# Additional test accounts
Email: <EMAIL>
Password: password123

Email: <EMAIL>
Password: admin123
```

#### Alternative Setup Commands

```bash
# Infrastructure only (database + gateway)
./scripts/dev-setup.sh

# Check status of all services
./scripts/dev-status.sh

# Stop all services
./scripts/dev-stop.sh
```

#### Manual Service Startup (Alternative)

If you prefer to start services manually:

```bash
# 1. Start infrastructure
docker-compose -f docker-compose.dev.yml up -d postgres gateway

# 2. Start backend services (in separate terminals)
bazel run //services/auth:auth
bazel run //apps/crm/backend:crm_backend  
bazel run //apps/project-hub/backend:project_hub_backend

# 3. Start frontend services (in separate terminals)
bazel run //apps/crm/web:dev
bazel run //apps/project-hub/frontend:dev
```

#### Architecture

The development environment uses a **hybrid architecture**:

- **Docker Containers**: Database and Gateway (for consistent infrastructure)
- **Local Processes**: Application services (for fast development and debugging)

**Benefits:**
- 🚀 **Fast Development**: Local services restart quickly with hot reload
- 🔒 **Consistent Infrastructure**: Docker ensures reliable database and routing
- 🛠️ **Easy Debugging**: Local services accessible with debuggers and IDE integration
- 💾 **Data Persistence**: Docker volumes preserve database state across restarts

#### Troubleshooting

```bash
# Check if all services are running
lsof -i :5432 -i :8080 -i :8003 -i :8004 -i :8005 -i :8081 -i :8082

# View service logs
tail -f auth.log crm_backend.log project_hub.log

# Test database connection
PGPASSWORD=orbit_password psql -h localhost -p 5432 -U orbit_user -d orbit

# Test API endpoints
curl http://localhost:8080/health
curl http://localhost:8080/api/projects
```

#### Team Recommendation

**Use `./scripts/zero2hero.sh` for all development setup needs.** This script is maintained to provide consistent, reliable environment setup for the entire team. It handles edge cases, provides comprehensive validation, and ensures everyone has the same working environment.

