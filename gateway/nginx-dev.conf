server {
    listen 8080;
    server_name localhost;

    # Root redirect to CRM (like the original gateway)
    location = / {
        return 301 /crm/;
    }

    # ProjectHub routing - /hub/ → localhost:8082 (local dev server)
    location /hub/ {
        proxy_pass http://host.docker.internal:8082/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support for Vite HMR
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Remove the /hub prefix when forwarding to frontend
        proxy_redirect off;
    }

    # CRM routing - /crm/ → localhost:8081/crm/ (local dev server)
    location /crm/ {
        proxy_pass http://host.docker.internal:8081/crm/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support for Vite HMR
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        proxy_redirect off;
    }

    # ProjectHub API routing - /api/ → localhost:8003 (local backend)
    location /api/auth {
        proxy_pass http://host.docker.internal:8003/api/auth;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Content-Type application/json;
    }
    
    location /api/projects {
        proxy_pass http://host.docker.internal:8003/api/projects;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Content-Type application/json;
    }

    location /api/team-members {
        proxy_pass http://host.docker.internal:8003/api/team-members;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Content-Type application/json;
    }

    location /api/impact-types {
        proxy_pass http://host.docker.internal:8003/api/impact-types;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Content-Type application/json;
    }
    
    # Catch-all for other ProjectHub API routes
    location ~ ^/api/(requirements|ai-analysis|admin|prd|ai-providers) {
        proxy_pass http://host.docker.internal:8003$request_uri;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Content-Type application/json;
    }

    # Auth API routing - /api/v1/auth/* → localhost:8004 (local auth service)
    location /api/v1/auth/ {
        proxy_pass http://host.docker.internal:8004/api/v1/auth/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Content-Type application/json;
    }

    # CRM API routing - /api/v1/ → localhost:8005 (local CRM backend)
    location /api/v1/ {
        proxy_pass http://host.docker.internal:8005/api/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Content-Type application/json;
    }

    # Vite development assets - forward to appropriate dev server
    location ~* ^/@vite/ {
        # Determine which app based on referer or default to ProjectHub
        proxy_pass http://host.docker.internal:8082$request_uri;
        proxy_set_header Host $host;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location /@react-refresh {
        proxy_pass http://host.docker.internal:8082$request_uri;
        proxy_set_header Host $host;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location ~* ^/src/ {
        proxy_pass http://host.docker.internal:8082$request_uri;
        proxy_set_header Host $host;
    }

    location ~* ^/node_modules/ {
        proxy_pass http://host.docker.internal:8082$request_uri;
        proxy_set_header Host $host;
    }

    location ~* ^/@fs/ {
        proxy_pass http://host.docker.internal:8082$request_uri;
        proxy_set_header Host $host;
    }

    # ProjectHub static assets (images, logos, etc.)
    location ~* ^/hub/(.*\.(png|jpg|jpeg|gif|ico|svg|css|js|woff|woff2|ttf|eot))$ {
        proxy_pass http://host.docker.internal:8082/$1;
        proxy_set_header Host $host;
    }

    # CRM-specific Vite assets
    location ~* ^/crm/@vite/ {
        proxy_pass http://host.docker.internal:8081$request_uri;
        proxy_set_header Host $host;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location ~* ^/crm/@react-refresh {
        proxy_pass http://host.docker.internal:8081$request_uri;
        proxy_set_header Host $host;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location ~* ^/crm/src/ {
        proxy_pass http://host.docker.internal:8081$request_uri;
        proxy_set_header Host $host;
    }

    # CRM static assets (images, logos, etc.)
    location ~* ^/crm/(.*\.(png|jpg|jpeg|gif|ico|svg|css|js|woff|woff2|ttf|eot))$ {
        proxy_pass http://host.docker.internal:8081/$1;
        proxy_set_header Host $host;
    }

    # Static assets - try both dev servers
    location ~* ^/(.*\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot))$ {
        # Try ProjectHub first, then CRM
        try_files $uri @projecthub @crm;
    }

    location @projecthub {
        proxy_pass http://host.docker.internal:8082/$1;
        proxy_set_header Host $host;
    }

    location @crm {
        proxy_pass http://host.docker.internal:8081/$1;
        proxy_set_header Host $host;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "nginx gateway healthy\n";
        add_header Content-Type text/plain;
    }

    # Error pages
    error_page 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}