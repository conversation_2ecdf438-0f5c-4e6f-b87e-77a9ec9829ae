package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/TwoDotAi/orbit/apps/crm/backend/config"
)

type ValidateTokenRequest struct {
	Token string `json:"token"`
}

type ValidateTokenResponse struct {
	Valid  bool   `json:"valid"`
	UserID string `json:"user_id,omitempty"`
	Email  string `json:"email,omitempty"`
	Error  string `json:"error,omitempty"`
}

func AuthRequired() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.<PERSON><PERSON>(http.StatusUnauthorized, gin.H{"error": "Bearer token required"})
			c.Abort()
			return
		}

		// Validate token with auth service
		cfg := config.Load()
		authServiceURL := cfg.AuthServiceURL
		if authServiceURL == "" {
			authServiceURL = "http://localhost:8004"
		}

		// Create validation request
		reqBody := ValidateTokenRequest{Token: tokenString}
		jsonData, err := json.Marshal(reqBody)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to marshal request"})
			c.Abort()
			return
		}

		// Call auth service to validate token
		client := &http.Client{Timeout: 5 * time.Second}
		resp, err := client.Post(
			fmt.Sprintf("%s/api/v1/auth/validate", authServiceURL),
			"application/json",
			bytes.NewBuffer(jsonData),
		)
		if err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Auth service unavailable"})
			c.Abort()
			return
		}
		defer resp.Body.Close()

		// Parse response
		var validateResp ValidateTokenResponse
		if err := json.NewDecoder(resp.Body).Decode(&validateResp); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse auth response"})
			c.Abort()
			return
		}

		if !validateResp.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		// Set user context
		c.Set("user_id", validateResp.UserID)
		c.Set("user_email", validateResp.Email)
		c.Next()
	})
}

func ErrorHandler() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		c.Next()

		// Handle any errors that occurred during request processing
		if len(c.Errors) > 0 {
			err := c.Errors.Last()
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": err.Error(),
			})
		}
	})
}