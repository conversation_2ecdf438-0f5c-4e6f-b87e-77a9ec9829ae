package main

import (
	"os"

	"github.com/gin-gonic/gin"
	"github.com/TwoDotAi/orbit/apps/crm/backend/config"
	"github.com/TwoDotAi/orbit/apps/crm/backend/database"
	"github.com/TwoDotAi/orbit/apps/crm/backend/handlers"
	"github.com/TwoDotAi/orbit/apps/crm/backend/middleware"
	"github.com/TwoDotAi/orbit/libs/go/logging"
	"go.uber.org/zap"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize logger
	logging.InitLogger("CRM_BACKEND", logging.IsDevelopment())
	defer logging.Sync()

	logging.ConfigMessage("CRM_BACKEND", cfg.Environment)

	// Initialize database
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		logging.LogErrorWithExit(err, "Failed to initialize database",
			zap.String("database_url", cfg.DatabaseURL),
		)
	}
	defer db.Close()

	logging.Info("Database initialized successfully",
		zap.String("database_url", cfg.DatabaseURL),
	)

	// Note: Database migrations are handled centrally via `bazel run //db:migrate`
	// Skipping application-level migrations to avoid conflicts

	// Initialize Gin router without default middleware
	r := gin.New()

	// Apply custom middleware (replaces gin.Default() middleware)
	r.Use(gin.Recovery()) // Keep recovery middleware
	r.Use(middleware.CORS())
	r.Use(logging.ZapLogger("CRM_BACKEND")) // Enhanced Zap logger
	r.Use(logging.ErrorHandler("CRM_BACKEND")) // Enhanced error handler

	// Health check endpoint
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "healthy",
			"service": "crm_backend",
		})
	})

	// API v1 routes
	v1 := r.Group("/api/v1")
	{
		// Protected routes (require authentication)
		protected := v1.Group("/")
		protected.Use(middleware.AuthRequired())
		{
			// User routes
			protected.GET("/users/profile", handlers.GetUserProfile)
			protected.PUT("/users/profile", handlers.UpdateUserProfile)

			// Company routes
			companies := protected.Group("/companies")
			{
				companies.GET("", handlers.ListCompanies)
				companies.POST("", handlers.CreateCompany)
				companies.GET("/:id", handlers.GetCompany)
				companies.PUT("/:id", handlers.UpdateCompany)
				companies.DELETE("/:id", handlers.DeleteCompany)
				companies.PUT("/:id/status", handlers.UpdateCompanyStatus)
			}

			// Company status routes
			protected.GET("/company-statuses", handlers.ListCompanyStatuses)

			// Contact routes
			contacts := protected.Group("/contacts")
			{
				contacts.GET("", handlers.ListContacts)
				contacts.POST("", handlers.CreateContact)
				contacts.GET("/:id", handlers.GetContact)
				contacts.PUT("/:id", handlers.UpdateContact)
				contacts.DELETE("/:id", handlers.DeleteContact)
				contacts.PUT("/:id/company", handlers.LinkContactToCompany)
				contacts.DELETE("/:id/company", handlers.UnlinkContactFromCompany)
			}

			// Deal routes
			deals := protected.Group("/deals")
			{
				deals.GET("", handlers.ListDeals)
				deals.POST("", handlers.CreateDeal)
				deals.GET("/:id", handlers.GetDeal)
				deals.PUT("/:id", handlers.UpdateDeal)
				deals.DELETE("/:id", handlers.DeleteDeal)
			}

			// Deal stage routes
			protected.GET("/deal-stages", handlers.ListDealStages)
		}
	}

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8003"
	}

	logging.StartupMessage("CRM_BACKEND", port)
	if err := r.Run("0.0.0.0:" + port); err != nil {
		logging.LogErrorWithExit(err, "Failed to start server",
			zap.String("port", port),
			zap.String("address", "0.0.0.0:"+port),
		)
	}
}