load("@gazelle//:def.bzl", "gazelle")
load("//tools:go_lint.bzl", "go_lint_all_test")

gazelle(name = "gazelle")
# gazelle:prefix github.com/TwoDotAi/orbit
# gazelle:resolve go github.com/TwoDotAi/orbit/api/openapi/generated/go //api/openapi:openapi_server

gazelle(
    name = "gazelle-update-repos",
    args = [
        "-from_file=go.mod",
        "-to_macro=deps.bzl%go_dependencies",
        "-prune",
    ],
    command = "update-repos",
)

# Aggregate targets for convenience
filegroup(
    name = "all_services",
    srcs = [
        "//apps/crm/backend:crm_backend",
        "//apps/examples/fastapi-demo",
        "//apps/examples/gin-demo",
        "//apps/examples/spring-demo",
        "//services/auth",
        "//services/gateway",
    ],
    visibility = ["//visibility:public"],
)

# Platform target for the main application
filegroup(
    name = "platform",
    srcs = [
        "//apps/crm/backend",
        "//apps/crm/web",
        "//services/auth",
        "//services/gateway",
    ],
    visibility = ["//visibility:public"],
)

# Test suites
test_suite(
    name = "unit_tests",
    tags = ["unit"],
)

test_suite(
    name = "integration_tests",
    tags = ["integration"],
)

test_suite(
    name = "database_tests",
    tests = [
        "//infra/database/tests:database_test_suite",
        "//infra/database/tests:migration_test",
        "//infra/database/tests:sql_integration_test",
    ],
)

# golangci-lint configuration
exports_files([
    ".golangci.yml",
    "go.mod",
])

filegroup(
    name = "golangci_config_file",
    srcs = [".golangci.yml"],
    visibility = ["//visibility:public"],
)

# Go linting for all packages
go_lint_all_test(
    name = "go_lint_all",
    config = ":golangci_config_file",
    packages = [
        "./apps/crm/backend/...",
        "./apps/examples/gin-demo/...",
        "./libs/go/...",
        "./services/auth/...",
        "./services/gateway/...",
    ],
    tags = ["lint"],
)

# Test suites
test_suite(
    name = "lint_tests",
    tags = ["lint"],
    tests = [
        ":go_lint_all",
    ],
)

# All tests
test_suite(
    name = "all_tests",
    tests = [
        ":database_tests",
        ":integration_tests",
        ":lint_tests",
        ":unit_tests",
    ],
)

# Platform deployment targets (delegate to tools/scripts package)
alias(
    name = "deploy_platform_dev",
    actual = "//tools/scripts:deploy_platform_dev",
)

alias(
    name = "deploy_quick_dev",
    actual = "//tools/scripts:deploy_quick_dev",
)

alias(
    name = "dev_setup",
    actual = "//tools/scripts:dev_setup",
)

alias(
    name = "setup_auto_migration",
    actual = "//tools/scripts:setup_auto_migration",
)

alias(
    name = "terraform_auto_deploy",
    actual = "//tools/scripts:terraform_auto_deploy",
)

# Docker-based Development Workflow Targets

# Start the complete development environment
sh_binary(
    name = "start_dev_environment",
    srcs = ["scripts/dev-setup.sh"],
    data = [
        "docker-compose.yml",
        "docker-compose.dev.yml",
        "//gateway:nginx-dev.conf",
        "//gateway:Dockerfile",
        "//gateway:nginx.conf",
        "//infra/database:container_seed_data.sql",
    ],
    visibility = ["//visibility:public"],
)

# Start all backend services locally
genrule(
    name = "start_backend_services",
    outs = ["backend_services_info.txt"],
    cmd = """
    echo "=== Starting Backend Services ===" > $(location backend_services_info.txt) && \\
    echo "Run these commands in separate terminals:" >> $(location backend_services_info.txt) && \\
    echo "" >> $(location backend_services_info.txt) && \\
    echo "Terminal 1: bazel run //services/auth:auth_service" >> $(location backend_services_info.txt) && \\
    echo "Terminal 2: bazel run //apps/crm/backend:crm_backend" >> $(location backend_services_info.txt) && \\
    echo "Terminal 3: bazel run //apps/project-hub/backend:project_hub_backend" >> $(location backend_services_info.txt) && \\
    echo "" >> $(location backend_services_info.txt) && \\
    echo "Or use: bazel run :start_backend_parallel" >> $(location backend_services_info.txt) && \\
    cat $(location backend_services_info.txt)
    """,
    visibility = ["//visibility:public"],
)

# Start all frontend services locally
genrule(
    name = "start_frontend_services",
    outs = ["frontend_services_info.txt"],
    cmd = """
    echo "=== Starting Frontend Services ===" > $(location frontend_services_info.txt) && \\
    echo "Run these commands in separate terminals:" >> $(location frontend_services_info.txt) && \\
    echo "" >> $(location frontend_services_info.txt) && \\
    echo "Terminal 1: bazel run //apps/crm/web:dev" >> $(location frontend_services_info.txt) && \\
    echo "Terminal 2: bazel run //apps/project-hub/frontend:dev" >> $(location frontend_services_info.txt) && \\
    echo "" >> $(location frontend_services_info.txt) && \\
    echo "Then access at http://localhost:8080" >> $(location frontend_services_info.txt) && \\
    cat $(location frontend_services_info.txt)
    """,
    visibility = ["//visibility:public"],
)

# Start backend services in parallel (for CI/testing)
genrule(
    name = "start_backend_parallel",
    outs = ["backend_parallel_started.txt"],
    cmd = """
    echo "=== Starting Backend Services in Parallel ===" > $(location backend_parallel_started.txt) && \\
    echo "This is primarily for testing/CI - use separate terminals for development" >> $(location backend_parallel_started.txt) && \\
    echo "Starting auth service..." >> $(location backend_parallel_started.txt) && \\
    bazel run //services/auth:auth_service &> auth.log & AUTH_PID=$$! && \\
    echo "Starting CRM backend..." >> $(location backend_parallel_started.txt) && \\
    bazel run //apps/crm/backend:crm_backend &> crm.log & CRM_PID=$$! && \\
    echo "Starting ProjectHub backend..." >> $(location backend_parallel_started.txt) && \\
    bazel run //apps/project-hub/backend:project_hub_backend &> hub.log & HUB_PID=$$! && \\
    echo "Backend services started with PIDs: Auth=$$AUTH_PID, CRM=$$CRM_PID, Hub=$$HUB_PID" >> $(location backend_parallel_started.txt) && \\
    echo "Logs: auth.log, crm.log, hub.log" >> $(location backend_parallel_started.txt) && \\
    cat $(location backend_parallel_started.txt)
    """,
    visibility = ["//visibility:public"],
)

# Stop all Docker services
sh_binary(
    name = "stop_dev_environment",
    srcs = ["scripts/dev-stop.sh"],
    data = [
        "docker-compose.yml",
        "docker-compose.dev.yml",
    ],
    visibility = ["//visibility:public"],
)

# Zero to Hero - Complete setup, build, deploy, and test workflow
sh_binary(
    name = "zero2hero",
    srcs = ["scripts/zero2hero.sh"],
    data = [
        "docker-compose.yml",
        "docker-compose.dev.yml",
        "//gateway:nginx-dev.conf",
        "//gateway:Dockerfile",
        "//gateway:nginx.conf",
        "//infra/database:container_seed_data.sql",
        "//infra/database:comprehensive_seed_data.sql",
    ],
    visibility = ["//visibility:public"],
)


# Test complete development environment
genrule(
    name = "test_dev_environment",
    outs = ["dev_environment_test.txt"],
    cmd = """
    echo "=== Testing Development Environment ===" > $(location dev_environment_test.txt) && \\
    echo "Database status:" >> $(location dev_environment_test.txt) && \\
    docker-compose -f docker-compose.dev.yml ps postgres >> $(location dev_environment_test.txt) 2>/dev/null || echo "Database not running" >> $(location dev_environment_test.txt) && \\
    echo "" >> $(location dev_environment_test.txt) && \\
    echo "Gateway status:" >> $(location dev_environment_test.txt) && \\
    docker-compose -f docker-compose.dev.yml ps gateway >> $(location dev_environment_test.txt) 2>/dev/null || echo "Gateway not running" >> $(location dev_environment_test.txt) && \\
    echo "" >> $(location dev_environment_test.txt) && \\
    echo "Gateway health check:" >> $(location dev_environment_test.txt) && \\
    curl -s http://localhost:8080/health >> $(location dev_environment_test.txt) 2>/dev/null || echo "Gateway health check failed" >> $(location dev_environment_test.txt) && \\
    echo "" >> $(location dev_environment_test.txt) && \\
    echo "Database connection test:" >> $(location dev_environment_test.txt) && \\
    PGPASSWORD=orbit_password psql -h localhost -p 5432 -U orbit_user -d orbit -c "SELECT 'Connected' as status;" >> $(location dev_environment_test.txt) 2>/dev/null || echo "Database connection failed" >> $(location dev_environment_test.txt) && \\
    cat $(location dev_environment_test.txt)
    """,
    visibility = ["//visibility:public"],
)

# Production Docker Compose targets
genrule(
    name = "start_production",
    outs = ["production_started.txt"],
    cmd = """
    echo "=== Starting Production Environment ===" > $(location production_started.txt) && \\
    docker-compose up -d --build && \\
    echo "Production environment started successfully" >> $(location production_started.txt) && \\
    echo "Access at http://localhost:8080" >> $(location production_started.txt) && \\
    cat $(location production_started.txt)
    """,
    visibility = ["//visibility:public"],
)

# Status of all services
sh_binary(
    name = "status_all_services",
    srcs = ["scripts/dev-status.sh"],
    data = [
        "docker-compose.yml",
        "docker-compose.dev.yml",
    ],
    visibility = ["//visibility:public"],
)

# Clean target to remove generated files
sh_binary(
    name = "clean",
    srcs = ["clean.sh"],
)
