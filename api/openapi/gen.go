// Package openapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package openapi

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	BearerAuthScopes = "bearerAuth.Scopes"
)

// Defines values for DocumentDetailsProcessingStatus.
const (
	Completed  DocumentDetailsProcessingStatus = "completed"
	Failed     DocumentDetailsProcessingStatus = "failed"
	Pending    DocumentDetailsProcessingStatus = "pending"
	Processing DocumentDetailsProcessingStatus = "processing"
)

// Defines values for InteractionInteractionType.
const (
	InteractionInteractionTypeDemo     InteractionInteractionType = "demo"
	InteractionInteractionTypeEmail    InteractionInteractionType = "email"
	InteractionInteractionTypeFollowUp InteractionInteractionType = "follow-up"
	InteractionInteractionTypeMeeting  InteractionInteractionType = "meeting"
	InteractionInteractionTypeOther    InteractionInteractionType = "other"
	InteractionInteractionTypePhone    InteractionInteractionType = "phone"
	InteractionInteractionTypeProposal InteractionInteractionType = "proposal"
)

// Defines values for InteractionCreateInteractionType.
const (
	InteractionCreateInteractionTypeDemo     InteractionCreateInteractionType = "demo"
	InteractionCreateInteractionTypeEmail    InteractionCreateInteractionType = "email"
	InteractionCreateInteractionTypeFollowUp InteractionCreateInteractionType = "follow-up"
	InteractionCreateInteractionTypeMeeting  InteractionCreateInteractionType = "meeting"
	InteractionCreateInteractionTypeOther    InteractionCreateInteractionType = "other"
	InteractionCreateInteractionTypePhone    InteractionCreateInteractionType = "phone"
	InteractionCreateInteractionTypeProposal InteractionCreateInteractionType = "proposal"
)

// Defines values for InteractionDetailsInteractionType.
const (
	InteractionDetailsInteractionTypeDemo     InteractionDetailsInteractionType = "demo"
	InteractionDetailsInteractionTypeEmail    InteractionDetailsInteractionType = "email"
	InteractionDetailsInteractionTypeFollowUp InteractionDetailsInteractionType = "follow-up"
	InteractionDetailsInteractionTypeMeeting  InteractionDetailsInteractionType = "meeting"
	InteractionDetailsInteractionTypeOther    InteractionDetailsInteractionType = "other"
	InteractionDetailsInteractionTypePhone    InteractionDetailsInteractionType = "phone"
	InteractionDetailsInteractionTypeProposal InteractionDetailsInteractionType = "proposal"
)

// Defines values for InteractionUpdateInteractionType.
const (
	InteractionUpdateInteractionTypeDemo     InteractionUpdateInteractionType = "demo"
	InteractionUpdateInteractionTypeEmail    InteractionUpdateInteractionType = "email"
	InteractionUpdateInteractionTypeFollowUp InteractionUpdateInteractionType = "follow-up"
	InteractionUpdateInteractionTypeMeeting  InteractionUpdateInteractionType = "meeting"
	InteractionUpdateInteractionTypeOther    InteractionUpdateInteractionType = "other"
	InteractionUpdateInteractionTypePhone    InteractionUpdateInteractionType = "phone"
	InteractionUpdateInteractionTypeProposal InteractionUpdateInteractionType = "proposal"
)

// Defines values for InteractionWithDetailsInteractionType.
const (
	Demo     InteractionWithDetailsInteractionType = "demo"
	Email    InteractionWithDetailsInteractionType = "email"
	FollowUp InteractionWithDetailsInteractionType = "follow-up"
	Meeting  InteractionWithDetailsInteractionType = "meeting"
	Other    InteractionWithDetailsInteractionType = "other"
	Phone    InteractionWithDetailsInteractionType = "phone"
	Proposal InteractionWithDetailsInteractionType = "proposal"
)

// Defines values for GetAuthCallbackProviderParamsProvider.
const (
	GetAuthCallbackProviderParamsProviderGoogle GetAuthCallbackProviderParamsProvider = "google"
)

// Defines values for PostAuthSigninOauthJSONBodyProvider.
const (
	PostAuthSigninOauthJSONBodyProviderGoogle PostAuthSigninOauthJSONBodyProvider = "google"
)

// AuthResponse defines model for AuthResponse.
type AuthResponse struct {
	AccessToken  *string `json:"access_token,omitempty"`
	ExpiresIn    *int    `json:"expires_in,omitempty"`
	RefreshToken *string `json:"refresh_token,omitempty"`
	TokenType    *string `json:"token_type,omitempty"`
	User         *User   `json:"user,omitempty"`
}

// Company defines model for Company.
type Company struct {
	Address         *string             `json:"address,omitempty"`
	CompanyStatusId *openapi_types.UUID `json:"company_status_id,omitempty"`
	CreatedAt       *time.Time          `json:"created_at,omitempty"`
	Id              *openapi_types.UUID `json:"id,omitempty"`
	IsDeleted       *bool               `json:"is_deleted,omitempty"`
	Name            *string             `json:"name,omitempty"`
	Notes           *string             `json:"notes,omitempty"`
	Phone           *string             `json:"phone,omitempty"`
	UpdatedAt       *time.Time          `json:"updated_at,omitempty"`
	Website         *string             `json:"website,omitempty"`
}

// CompanyBasicInfo defines model for CompanyBasicInfo.
type CompanyBasicInfo struct {
	Id   *openapi_types.UUID `json:"id,omitempty"`
	Name *string             `json:"name,omitempty"`
}

// CompanyCreate defines model for CompanyCreate.
type CompanyCreate struct {
	Address         *string             `json:"address,omitempty"`
	CompanyStatusId *openapi_types.UUID `json:"company_status_id,omitempty"`
	Name            string              `json:"name"`
	Notes           *string             `json:"notes,omitempty"`
	Phone           *string             `json:"phone,omitempty"`
	Website         *string             `json:"website,omitempty"`
}

// CompanyDetails defines model for CompanyDetails.
type CompanyDetails struct {
	Address         *string             `json:"address,omitempty"`
	CompanyStatusId *openapi_types.UUID `json:"company_status_id,omitempty"`
	Contacts        *[]Contact          `json:"contacts,omitempty"`
	CreatedAt       *time.Time          `json:"created_at,omitempty"`
	Id              *openapi_types.UUID `json:"id,omitempty"`
	IsDeleted       *bool               `json:"is_deleted,omitempty"`
	Name            *string             `json:"name,omitempty"`
	Notes           *string             `json:"notes,omitempty"`
	Phone           *string             `json:"phone,omitempty"`
	UpdatedAt       *time.Time          `json:"updated_at,omitempty"`
	Website         *string             `json:"website,omitempty"`
}

// CompanyInfo defines model for CompanyInfo.
type CompanyInfo struct {
	Id        *openapi_types.UUID `json:"id,omitempty"`
	IsDeleted *bool               `json:"is_deleted,omitempty"`
	Name      *string             `json:"name,omitempty"`
}

// CompanyStatus defines model for CompanyStatus.
type CompanyStatus struct {
	CreatedAt     *time.Time          `json:"created_at,omitempty"`
	Id            *openapi_types.UUID `json:"id,omitempty"`
	Name          *string             `json:"name,omitempty"`
	PipelineOrder *int                `json:"pipeline_order,omitempty"`
}

// CompanyUpdate defines model for CompanyUpdate.
type CompanyUpdate struct {
	Address         *string             `json:"address,omitempty"`
	CompanyStatusId *openapi_types.UUID `json:"company_status_id,omitempty"`
	Name            *string             `json:"name,omitempty"`
	Notes           *string             `json:"notes,omitempty"`
	Phone           *string             `json:"phone,omitempty"`
	Website         *string             `json:"website,omitempty"`
}

// Contact defines model for Contact.
type Contact struct {
	CompanyId *openapi_types.UUID  `json:"company_id"`
	CreatedAt *time.Time           `json:"created_at,omitempty"`
	CreatedBy *openapi_types.UUID  `json:"created_by,omitempty"`
	Email     *openapi_types.Email `json:"email,omitempty"`
	FirstName *string              `json:"first_name,omitempty"`
	Id        *openapi_types.UUID  `json:"id,omitempty"`
	IsDeleted *bool                `json:"is_deleted,omitempty"`
	JobTitle  *string              `json:"job_title,omitempty"`
	LastName  *string              `json:"last_name,omitempty"`
	Phone     *string              `json:"phone,omitempty"`
	UpdatedAt *time.Time           `json:"updated_at,omitempty"`
}

// ContactCreate defines model for ContactCreate.
type ContactCreate struct {
	CompanyId *openapi_types.UUID  `json:"company_id,omitempty"`
	Email     *openapi_types.Email `json:"email,omitempty"`
	FirstName string               `json:"first_name"`
	JobTitle  *string              `json:"job_title,omitempty"`
	LastName  string               `json:"last_name"`
	Phone     *string              `json:"phone,omitempty"`
}

// ContactDetails defines model for ContactDetails.
type ContactDetails struct {
	Company   *CompanyBasicInfo    `json:"company"`
	CompanyId *openapi_types.UUID  `json:"company_id"`
	CreatedAt *time.Time           `json:"created_at,omitempty"`
	CreatedBy *openapi_types.UUID  `json:"created_by,omitempty"`
	Email     *openapi_types.Email `json:"email,omitempty"`
	FirstName *string              `json:"first_name,omitempty"`
	Id        *openapi_types.UUID  `json:"id,omitempty"`
	IsDeleted *bool                `json:"is_deleted,omitempty"`
	JobTitle  *string              `json:"job_title,omitempty"`
	LastName  *string              `json:"last_name,omitempty"`
	Phone     *string              `json:"phone,omitempty"`
	UpdatedAt *time.Time           `json:"updated_at,omitempty"`
}

// ContactUpdate defines model for ContactUpdate.
type ContactUpdate struct {
	CompanyId *openapi_types.UUID  `json:"company_id"`
	Email     *openapi_types.Email `json:"email,omitempty"`
	FirstName *string              `json:"first_name,omitempty"`
	JobTitle  *string              `json:"job_title,omitempty"`
	LastName  *string              `json:"last_name,omitempty"`
	Phone     *string              `json:"phone,omitempty"`
}

// ContactWithCompany defines model for ContactWithCompany.
type ContactWithCompany struct {
	Company   *CompanyInfo         `json:"company"`
	CompanyId *openapi_types.UUID  `json:"company_id"`
	CreatedAt *time.Time           `json:"created_at,omitempty"`
	CreatedBy *openapi_types.UUID  `json:"created_by,omitempty"`
	Email     *openapi_types.Email `json:"email,omitempty"`
	FirstName *string              `json:"first_name,omitempty"`
	Id        *openapi_types.UUID  `json:"id,omitempty"`
	IsDeleted *bool                `json:"is_deleted,omitempty"`
	JobTitle  *string              `json:"job_title,omitempty"`
	LastName  *string              `json:"last_name,omitempty"`
	Phone     *string              `json:"phone,omitempty"`
	UpdatedAt *time.Time           `json:"updated_at,omitempty"`
}

// Deal defines model for Deal.
type Deal struct {
	CompanyId         *openapi_types.UUID `json:"company_id,omitempty"`
	CreatedAt         *time.Time          `json:"created_at,omitempty"`
	CreatedBy         *openapi_types.UUID `json:"created_by,omitempty"`
	DealStageId       *openapi_types.UUID `json:"deal_stage_id,omitempty"`
	Description       *string             `json:"description,omitempty"`
	EstimatedValue    *float32            `json:"estimated_value,omitempty"`
	ExpectedCloseDate *openapi_types.Date `json:"expected_close_date,omitempty"`
	Id                *openapi_types.UUID `json:"id,omitempty"`
	Title             *string             `json:"title,omitempty"`
	UpdatedAt         *time.Time          `json:"updated_at,omitempty"`
}

// DealCompanyInfo defines model for DealCompanyInfo.
type DealCompanyInfo struct {
	Id   *openapi_types.UUID `json:"id,omitempty"`
	Name *string             `json:"name,omitempty"`
}

// DealCreate defines model for DealCreate.
type DealCreate struct {
	CompanyId         openapi_types.UUID  `json:"company_id"`
	DealStageId       openapi_types.UUID  `json:"deal_stage_id"`
	Description       *string             `json:"description,omitempty"`
	EstimatedValue    *float32            `json:"estimated_value,omitempty"`
	ExpectedCloseDate *openapi_types.Date `json:"expected_close_date,omitempty"`
	Title             string              `json:"title"`
}

// DealDetails defines model for DealDetails.
type DealDetails struct {
	Company *struct {
		Id      *openapi_types.UUID `json:"id,omitempty"`
		Name    *string             `json:"name,omitempty"`
		Phone   *string             `json:"phone,omitempty"`
		Website *string             `json:"website,omitempty"`
	} `json:"company,omitempty"`
	CompanyId *openapi_types.UUID `json:"company_id,omitempty"`
	CreatedAt *time.Time          `json:"created_at,omitempty"`
	CreatedBy *openapi_types.UUID `json:"created_by,omitempty"`
	DealStage *struct {
		Id            *openapi_types.UUID `json:"id,omitempty"`
		IsClosedLost  *bool               `json:"is_closed_lost,omitempty"`
		IsClosedWon   *bool               `json:"is_closed_won,omitempty"`
		Name          *string             `json:"name,omitempty"`
		PipelineOrder *int                `json:"pipeline_order,omitempty"`
	} `json:"deal_stage,omitempty"`
	DealStageId       *openapi_types.UUID `json:"deal_stage_id,omitempty"`
	Description       *string             `json:"description,omitempty"`
	EstimatedValue    *float32            `json:"estimated_value,omitempty"`
	ExpectedCloseDate *openapi_types.Date `json:"expected_close_date,omitempty"`
	Id                *openapi_types.UUID `json:"id,omitempty"`
	Title             *string             `json:"title,omitempty"`
	UpdatedAt         *time.Time          `json:"updated_at,omitempty"`
}

// DealStage defines model for DealStage.
type DealStage struct {
	CreatedAt     *time.Time          `json:"created_at,omitempty"`
	Id            *openapi_types.UUID `json:"id,omitempty"`
	IsClosedLost  *bool               `json:"is_closed_lost,omitempty"`
	IsClosedWon   *bool               `json:"is_closed_won,omitempty"`
	Name          *string             `json:"name,omitempty"`
	PipelineOrder *int                `json:"pipeline_order,omitempty"`
}

// DealStageInfo defines model for DealStageInfo.
type DealStageInfo struct {
	Id            *openapi_types.UUID `json:"id,omitempty"`
	Name          *string             `json:"name,omitempty"`
	PipelineOrder *int                `json:"pipeline_order,omitempty"`
}

// DealUpdate defines model for DealUpdate.
type DealUpdate struct {
	CompanyId         *openapi_types.UUID `json:"company_id,omitempty"`
	DealStageId       *openapi_types.UUID `json:"deal_stage_id,omitempty"`
	Description       *string             `json:"description,omitempty"`
	EstimatedValue    *float32            `json:"estimated_value,omitempty"`
	ExpectedCloseDate *openapi_types.Date `json:"expected_close_date,omitempty"`
	Title             *string             `json:"title,omitempty"`
}

// DealWithDetails defines model for DealWithDetails.
type DealWithDetails struct {
	Company           *DealCompanyInfo    `json:"company,omitempty"`
	CompanyId         *openapi_types.UUID `json:"company_id,omitempty"`
	CreatedAt         *time.Time          `json:"created_at,omitempty"`
	CreatedBy         *openapi_types.UUID `json:"created_by,omitempty"`
	DealStage         *DealStageInfo      `json:"deal_stage,omitempty"`
	DealStageId       *openapi_types.UUID `json:"deal_stage_id,omitempty"`
	Description       *string             `json:"description,omitempty"`
	EstimatedValue    *float32            `json:"estimated_value,omitempty"`
	ExpectedCloseDate *openapi_types.Date `json:"expected_close_date,omitempty"`
	Id                *openapi_types.UUID `json:"id,omitempty"`
	Title             *string             `json:"title,omitempty"`
	UpdatedAt         *time.Time          `json:"updated_at,omitempty"`
}

// Document defines model for Document.
type Document struct {
	Content    *string                 `json:"content,omitempty"`
	CreatedAt  *time.Time              `json:"created_at,omitempty"`
	FilterTags *[]string               `json:"filter_tags,omitempty"`
	Id         *openapi_types.UUID     `json:"id,omitempty"`
	Metadata   *map[string]interface{} `json:"metadata,omitempty"`
	SourceId   *string                 `json:"source_id,omitempty"`
	UpdatedAt  *time.Time              `json:"updated_at,omitempty"`
}

// DocumentCreate defines model for DocumentCreate.
type DocumentCreate struct {
	Content    string                  `json:"content"`
	FilterTags *[]string               `json:"filter_tags,omitempty"`
	Metadata   *map[string]interface{} `json:"metadata,omitempty"`
	SourceId   *string                 `json:"source_id,omitempty"`
}

// DocumentDetails defines model for DocumentDetails.
type DocumentDetails struct {
	Content          *string                          `json:"content,omitempty"`
	CreatedAt        *time.Time                       `json:"created_at,omitempty"`
	FilterTags       *[]string                        `json:"filter_tags,omitempty"`
	Id               *openapi_types.UUID              `json:"id,omitempty"`
	Metadata         *map[string]interface{}          `json:"metadata,omitempty"`
	ProcessingStatus *DocumentDetailsProcessingStatus `json:"processing_status,omitempty"`
	SourceId         *string                          `json:"source_id,omitempty"`
	UpdatedAt        *time.Time                       `json:"updated_at,omitempty"`
	VectorEmbeddings *[]float32                       `json:"vector_embeddings,omitempty"`
}

// DocumentDetailsProcessingStatus defines model for DocumentDetails.ProcessingStatus.
type DocumentDetailsProcessingStatus string

// DocumentUpdate defines model for DocumentUpdate.
type DocumentUpdate struct {
	Content    *string                 `json:"content,omitempty"`
	FilterTags *[]string               `json:"filter_tags,omitempty"`
	Metadata   *map[string]interface{} `json:"metadata,omitempty"`
	SourceId   *string                 `json:"source_id,omitempty"`
}

// Error defines model for Error.
type Error struct {
	Code    string                  `json:"code"`
	Details *map[string]interface{} `json:"details,omitempty"`
	Message string                  `json:"message"`
}

// ErrorResponse defines model for ErrorResponse.
type ErrorResponse struct {
	Error   *string `json:"error,omitempty"`
	Message *string `json:"message,omitempty"`
}

// FilterTag defines model for FilterTag.
type FilterTag struct {
	Color     *string             `json:"color,omitempty"`
	CreatedAt *time.Time          `json:"created_at,omitempty"`
	Id        *openapi_types.UUID `json:"id,omitempty"`
	Name      *string             `json:"name,omitempty"`
}

// FilterTagCreate defines model for FilterTagCreate.
type FilterTagCreate struct {
	Color *string `json:"color,omitempty"`
	Name  string  `json:"name"`
}

// Interaction defines model for Interaction.
type Interaction struct {
	CompanyId           *openapi_types.UUID         `json:"company_id"`
	ContactId           *openapi_types.UUID         `json:"contact_id"`
	CreatedAt           *time.Time                  `json:"created_at,omitempty"`
	CreatedBy           *openapi_types.UUID         `json:"created_by,omitempty"`
	Id                  *openapi_types.UUID         `json:"id,omitempty"`
	InteractionDatetime *time.Time                  `json:"interaction_datetime,omitempty"`
	InteractionType     *InteractionInteractionType `json:"interaction_type,omitempty"`
	Notes               *string                     `json:"notes,omitempty"`
	UpdatedAt           *time.Time                  `json:"updated_at,omitempty"`
}

// InteractionInteractionType defines model for Interaction.InteractionType.
type InteractionInteractionType string

// InteractionCreate defines model for InteractionCreate.
type InteractionCreate struct {
	CompanyId           *openapi_types.UUID              `json:"company_id,omitempty"`
	ContactId           *openapi_types.UUID              `json:"contact_id,omitempty"`
	InteractionDatetime time.Time                        `json:"interaction_datetime"`
	InteractionType     InteractionCreateInteractionType `json:"interaction_type"`
	Notes               string                           `json:"notes"`
}

// InteractionCreateInteractionType defines model for InteractionCreate.InteractionType.
type InteractionCreateInteractionType string

// InteractionDetails defines model for InteractionDetails.
type InteractionDetails struct {
	Company *struct {
		Id      *openapi_types.UUID `json:"id,omitempty"`
		Name    *string             `json:"name,omitempty"`
		Phone   *string             `json:"phone,omitempty"`
		Website *string             `json:"website,omitempty"`
	} `json:"company"`
	CompanyId *openapi_types.UUID `json:"company_id"`
	Contact   *struct {
		Email     *string             `json:"email,omitempty"`
		FirstName *string             `json:"first_name,omitempty"`
		Id        *openapi_types.UUID `json:"id,omitempty"`
		JobTitle  *string             `json:"job_title,omitempty"`
		LastName  *string             `json:"last_name,omitempty"`
		Phone     *string             `json:"phone,omitempty"`
	} `json:"contact"`
	ContactId           *openapi_types.UUID                `json:"contact_id"`
	CreatedAt           *time.Time                         `json:"created_at,omitempty"`
	CreatedBy           *openapi_types.UUID                `json:"created_by,omitempty"`
	Id                  *openapi_types.UUID                `json:"id,omitempty"`
	InteractionDatetime *time.Time                         `json:"interaction_datetime,omitempty"`
	InteractionType     *InteractionDetailsInteractionType `json:"interaction_type,omitempty"`
	Notes               *string                            `json:"notes,omitempty"`
	UpdatedAt           *time.Time                         `json:"updated_at,omitempty"`
}

// InteractionDetailsInteractionType defines model for InteractionDetails.InteractionType.
type InteractionDetailsInteractionType string

// InteractionUpdate defines model for InteractionUpdate.
type InteractionUpdate struct {
	CompanyId           *openapi_types.UUID               `json:"company_id,omitempty"`
	ContactId           *openapi_types.UUID               `json:"contact_id,omitempty"`
	InteractionDatetime *time.Time                        `json:"interaction_datetime,omitempty"`
	InteractionType     *InteractionUpdateInteractionType `json:"interaction_type,omitempty"`
	Notes               *string                           `json:"notes,omitempty"`
}

// InteractionUpdateInteractionType defines model for InteractionUpdate.InteractionType.
type InteractionUpdateInteractionType string

// InteractionWithDetails defines model for InteractionWithDetails.
type InteractionWithDetails struct {
	Company *struct {
		Id   *openapi_types.UUID `json:"id,omitempty"`
		Name *string             `json:"name,omitempty"`
	} `json:"company"`
	CompanyId *openapi_types.UUID `json:"company_id"`
	Contact   *struct {
		Email     *string             `json:"email,omitempty"`
		FirstName *string             `json:"first_name,omitempty"`
		Id        *openapi_types.UUID `json:"id,omitempty"`
		LastName  *string             `json:"last_name,omitempty"`
	} `json:"contact"`
	ContactId           *openapi_types.UUID                    `json:"contact_id"`
	CreatedAt           *time.Time                             `json:"created_at,omitempty"`
	CreatedBy           *openapi_types.UUID                    `json:"created_by,omitempty"`
	Id                  *openapi_types.UUID                    `json:"id,omitempty"`
	InteractionDatetime *time.Time                             `json:"interaction_datetime,omitempty"`
	InteractionType     *InteractionWithDetailsInteractionType `json:"interaction_type,omitempty"`
	Notes               *string                                `json:"notes,omitempty"`
	UpdatedAt           *time.Time                             `json:"updated_at,omitempty"`
}

// InteractionWithDetailsInteractionType defines model for InteractionWithDetails.InteractionType.
type InteractionWithDetailsInteractionType string

// Session defines model for Session.
type Session struct {
	AccessToken  *string `json:"access_token,omitempty"`
	ExpiresIn    *int    `json:"expires_in,omitempty"`
	RefreshToken *string `json:"refresh_token,omitempty"`
	TokenType    *string `json:"token_type,omitempty"`
	User         *User   `json:"user,omitempty"`
}

// User defines model for User.
type User struct {
	CreatedAt        *time.Time           `json:"created_at,omitempty"`
	Email            *openapi_types.Email `json:"email,omitempty"`
	EmailConfirmedAt *time.Time           `json:"email_confirmed_at,omitempty"`
	Id               *openapi_types.UUID  `json:"id,omitempty"`
	UpdatedAt        *time.Time           `json:"updated_at,omitempty"`
}

// UserProfile defines model for UserProfile.
type UserProfile struct {
	AvatarUrl *string             `json:"avatar_url,omitempty"`
	CreatedAt *time.Time          `json:"created_at,omitempty"`
	FullName  *string             `json:"full_name,omitempty"`
	Id        *openapi_types.UUID `json:"id,omitempty"`
	Timezone  *string             `json:"timezone,omitempty"`
	UpdatedAt *time.Time          `json:"updated_at,omitempty"`
}

// UserProfileUpdate defines model for UserProfileUpdate.
type UserProfileUpdate struct {
	AvatarUrl *string `json:"avatar_url,omitempty"`
	FullName  *string `json:"full_name,omitempty"`
	Timezone  *string `json:"timezone,omitempty"`
}

// SchemasValidationError defines model for schemas_ValidationError.
type SchemasValidationError struct {
	Code    *string                 `json:"code,omitempty"`
	Details *map[string]interface{} `json:"details,omitempty"`
	Message *string                 `json:"message,omitempty"`
}

// GetArliDocumentsParams defines parameters for GetArliDocuments.
type GetArliDocumentsParams struct {
	// FilterTags Filter by tags
	FilterTags *[]string `form:"filter_tags,omitempty" json:"filter_tags,omitempty"`

	// MetadataFilter Filter by metadata properties
	MetadataFilter *map[string]interface{} `form:"metadata_filter,omitempty" json:"metadata_filter,omitempty"`
	Limit          *int                    `form:"limit,omitempty" json:"limit,omitempty"`
	Offset         *int                    `form:"offset,omitempty" json:"offset,omitempty"`
}

// PostArliDocumentsVectorizeJSONBody defines parameters for PostArliDocumentsVectorize.
type PostArliDocumentsVectorizeJSONBody struct {
	Content    string                  `json:"content"`
	DocumentId openapi_types.UUID      `json:"document_id"`
	Metadata   *map[string]interface{} `json:"metadata,omitempty"`
}

// GetAuthCallbackProviderParams defines parameters for GetAuthCallbackProvider.
type GetAuthCallbackProviderParams struct {
	Code  string  `form:"code" json:"code"`
	State string  `form:"state" json:"state"`
	Error *string `form:"error,omitempty" json:"error,omitempty"`
}

// GetAuthCallbackProviderParamsProvider defines parameters for GetAuthCallbackProvider.
type GetAuthCallbackProviderParamsProvider string

// PostAuthSigninJSONBody defines parameters for PostAuthSignin.
type PostAuthSigninJSONBody struct {
	Email    openapi_types.Email `json:"email"`
	Password string              `json:"password"`
}

// PostAuthSigninOauthJSONBody defines parameters for PostAuthSigninOauth.
type PostAuthSigninOauthJSONBody struct {
	Provider   PostAuthSigninOauthJSONBodyProvider `json:"provider"`
	RedirectTo *string                             `json:"redirectTo,omitempty"`
}

// PostAuthSigninOauthJSONBodyProvider defines parameters for PostAuthSigninOauth.
type PostAuthSigninOauthJSONBodyProvider string

// GetCompaniesParams defines parameters for GetCompanies.
type GetCompaniesParams struct {
	// StatusId Filter by company status
	StatusId *openapi_types.UUID `form:"status_id,omitempty" json:"status_id,omitempty"`

	// IncludeDeleted Include soft-deleted companies
	IncludeDeleted *bool `form:"include_deleted,omitempty" json:"include_deleted,omitempty"`
}

// PutCompaniesIdStatusJSONBody defines parameters for PutCompaniesIdStatus.
type PutCompaniesIdStatusJSONBody struct {
	CompanyStatusId openapi_types.UUID `json:"company_status_id"`
}

// GetContactsParams defines parameters for GetContacts.
type GetContactsParams struct {
	// CompanyId Filter by company ID
	CompanyId *openapi_types.UUID `form:"company_id,omitempty" json:"company_id,omitempty"`

	// Unlinked Only return contacts without a company
	Unlinked *bool `form:"unlinked,omitempty" json:"unlinked,omitempty"`

	// IncludeDeleted Include soft-deleted contacts
	IncludeDeleted *bool `form:"include_deleted,omitempty" json:"include_deleted,omitempty"`
}

// PutContactsIdCompanyJSONBody defines parameters for PutContactsIdCompany.
type PutContactsIdCompanyJSONBody struct {
	CompanyId openapi_types.UUID `json:"company_id"`
}

// GetDealsParams defines parameters for GetDeals.
type GetDealsParams struct {
	// StageId Filter by deal stage ID
	StageId *openapi_types.UUID `form:"stage_id,omitempty" json:"stage_id,omitempty"`

	// CompanyId Filter by company ID
	CompanyId *openapi_types.UUID `form:"company_id,omitempty" json:"company_id,omitempty"`
}

// GetInteractionsParams defines parameters for GetInteractions.
type GetInteractionsParams struct {
	// CompanyId Filter by company ID
	CompanyId *openapi_types.UUID `form:"company_id,omitempty" json:"company_id,omitempty"`

	// ContactId Filter by contact ID
	ContactId *openapi_types.UUID `form:"contact_id,omitempty" json:"contact_id,omitempty"`

	// InteractionType Filter by interaction type
	InteractionType *string `form:"interaction_type,omitempty" json:"interaction_type,omitempty"`

	// FromDate Filter interactions from this date
	FromDate *time.Time `form:"from_date,omitempty" json:"from_date,omitempty"`

	// ToDate Filter interactions to this date
	ToDate *time.Time `form:"to_date,omitempty" json:"to_date,omitempty"`

	// Limit Number of interactions to return
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Number of interactions to skip
	Offset *int `form:"offset,omitempty" json:"offset,omitempty"`
}

// PostArliDocumentsJSONRequestBody defines body for PostArliDocuments for application/json ContentType.
type PostArliDocumentsJSONRequestBody = DocumentCreate

// PostArliDocumentsVectorizeJSONRequestBody defines body for PostArliDocumentsVectorize for application/json ContentType.
type PostArliDocumentsVectorizeJSONRequestBody PostArliDocumentsVectorizeJSONBody

// PutArliDocumentsIdJSONRequestBody defines body for PutArliDocumentsId for application/json ContentType.
type PutArliDocumentsIdJSONRequestBody = DocumentUpdate

// PostArliFilterTagsJSONRequestBody defines body for PostArliFilterTags for application/json ContentType.
type PostArliFilterTagsJSONRequestBody = FilterTagCreate

// PostAuthSigninJSONRequestBody defines body for PostAuthSignin for application/json ContentType.
type PostAuthSigninJSONRequestBody PostAuthSigninJSONBody

// PostAuthSigninOauthJSONRequestBody defines body for PostAuthSigninOauth for application/json ContentType.
type PostAuthSigninOauthJSONRequestBody PostAuthSigninOauthJSONBody

// PostCompaniesJSONRequestBody defines body for PostCompanies for application/json ContentType.
type PostCompaniesJSONRequestBody = CompanyCreate

// PutCompaniesIdJSONRequestBody defines body for PutCompaniesId for application/json ContentType.
type PutCompaniesIdJSONRequestBody = CompanyUpdate

// PutCompaniesIdStatusJSONRequestBody defines body for PutCompaniesIdStatus for application/json ContentType.
type PutCompaniesIdStatusJSONRequestBody PutCompaniesIdStatusJSONBody

// PostContactsJSONRequestBody defines body for PostContacts for application/json ContentType.
type PostContactsJSONRequestBody = ContactCreate

// PutContactsIdJSONRequestBody defines body for PutContactsId for application/json ContentType.
type PutContactsIdJSONRequestBody = ContactUpdate

// PutContactsIdCompanyJSONRequestBody defines body for PutContactsIdCompany for application/json ContentType.
type PutContactsIdCompanyJSONRequestBody PutContactsIdCompanyJSONBody

// PostDealsJSONRequestBody defines body for PostDeals for application/json ContentType.
type PostDealsJSONRequestBody = DealCreate

// PutDealsIdJSONRequestBody defines body for PutDealsId for application/json ContentType.
type PutDealsIdJSONRequestBody = DealUpdate

// PostInteractionsJSONRequestBody defines body for PostInteractions for application/json ContentType.
type PostInteractionsJSONRequestBody = InteractionCreate

// PutInteractionsIdJSONRequestBody defines body for PutInteractionsId for application/json ContentType.
type PutInteractionsIdJSONRequestBody = InteractionUpdate

// PutUsersProfileJSONRequestBody defines body for PutUsersProfile for application/json ContentType.
type PutUsersProfileJSONRequestBody = UserProfileUpdate

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// List documents
	// (GET /arli/documents)
	GetArliDocuments(c *gin.Context, params GetArliDocumentsParams)
	// Create document
	// (POST /arli/documents)
	PostArliDocuments(c *gin.Context)
	// Vectorize document
	// (POST /arli/documents/vectorize)
	PostArliDocumentsVectorize(c *gin.Context)
	// Delete document
	// (DELETE /arli/documents/{id})
	DeleteArliDocumentsId(c *gin.Context, id openapi_types.UUID)
	// Get document
	// (GET /arli/documents/{id})
	GetArliDocumentsId(c *gin.Context, id openapi_types.UUID)
	// Update document
	// (PUT /arli/documents/{id})
	PutArliDocumentsId(c *gin.Context, id openapi_types.UUID)
	// List filter tags
	// (GET /arli/filter-tags)
	GetArliFilterTags(c *gin.Context)
	// Create filter tag
	// (POST /arli/filter-tags)
	PostArliFilterTags(c *gin.Context)
	// OAuth callback
	// (GET /auth/callback/{provider})
	GetAuthCallbackProvider(c *gin.Context, provider GetAuthCallbackProviderParamsProvider, params GetAuthCallbackProviderParams)
	// Get current session
	// (GET /auth/session)
	GetAuthSession(c *gin.Context)
	// Sign in with email/password
	// (POST /auth/signin)
	PostAuthSignin(c *gin.Context)
	// Sign in with OAuth
	// (POST /auth/signin/oauth)
	PostAuthSigninOauth(c *gin.Context)
	// Sign out
	// (POST /auth/signout)
	PostAuthSignout(c *gin.Context)
	// Get current user
	// (GET /auth/user)
	GetAuthUser(c *gin.Context)
	// List companies
	// (GET /companies)
	GetCompanies(c *gin.Context, params GetCompaniesParams)
	// Create company
	// (POST /companies)
	PostCompanies(c *gin.Context)
	// Delete company
	// (DELETE /companies/{id})
	DeleteCompaniesId(c *gin.Context, id openapi_types.UUID)
	// Get company
	// (GET /companies/{id})
	GetCompaniesId(c *gin.Context, id openapi_types.UUID)
	// Update company
	// (PUT /companies/{id})
	PutCompaniesId(c *gin.Context, id openapi_types.UUID)
	// Update company status
	// (PUT /companies/{id}/status)
	PutCompaniesIdStatus(c *gin.Context, id openapi_types.UUID)
	// List company statuses
	// (GET /company-statuses)
	GetCompanyStatuses(c *gin.Context)
	// List contacts
	// (GET /contacts)
	GetContacts(c *gin.Context, params GetContactsParams)
	// Create contact
	// (POST /contacts)
	PostContacts(c *gin.Context)
	// Delete contact
	// (DELETE /contacts/{id})
	DeleteContactsId(c *gin.Context, id openapi_types.UUID)
	// Get contact
	// (GET /contacts/{id})
	GetContactsId(c *gin.Context, id openapi_types.UUID)
	// Update contact
	// (PUT /contacts/{id})
	PutContactsId(c *gin.Context, id openapi_types.UUID)
	// Unlink contact from company
	// (DELETE /contacts/{id}/company)
	DeleteContactsIdCompany(c *gin.Context, id openapi_types.UUID)
	// Link contact to company
	// (PUT /contacts/{id}/company)
	PutContactsIdCompany(c *gin.Context, id openapi_types.UUID)
	// List deal stages
	// (GET /deal-stages)
	GetDealStages(c *gin.Context)
	// List deals
	// (GET /deals)
	GetDeals(c *gin.Context, params GetDealsParams)
	// Create deal
	// (POST /deals)
	PostDeals(c *gin.Context)
	// Delete deal
	// (DELETE /deals/{id})
	DeleteDealsId(c *gin.Context, id openapi_types.UUID)
	// Get deal
	// (GET /deals/{id})
	GetDealsId(c *gin.Context, id openapi_types.UUID)
	// Update deal
	// (PUT /deals/{id})
	PutDealsId(c *gin.Context, id openapi_types.UUID)
	// List interactions
	// (GET /interactions)
	GetInteractions(c *gin.Context, params GetInteractionsParams)
	// Create interaction
	// (POST /interactions)
	PostInteractions(c *gin.Context)
	// Delete interaction
	// (DELETE /interactions/{id})
	DeleteInteractionsId(c *gin.Context, id openapi_types.UUID)
	// Get interaction
	// (GET /interactions/{id})
	GetInteractionsId(c *gin.Context, id openapi_types.UUID)
	// Update interaction
	// (PUT /interactions/{id})
	PutInteractionsId(c *gin.Context, id openapi_types.UUID)
	// Get user profile
	// (GET /users/profile)
	GetUsersProfile(c *gin.Context)
	// Update user profile
	// (PUT /users/profile)
	PutUsersProfile(c *gin.Context)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetArliDocuments operation middleware
func (siw *ServerInterfaceWrapper) GetArliDocuments(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetArliDocumentsParams

	// ------------- Optional query parameter "filter_tags" -------------

	err = runtime.BindQueryParameter("form", true, false, "filter_tags", c.Request.URL.Query(), &params.FilterTags)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter filter_tags: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "metadata_filter" -------------

	err = runtime.BindQueryParameter("form", true, false, "metadata_filter", c.Request.URL.Query(), &params.MetadataFilter)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter metadata_filter: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetArliDocuments(c, params)
}

// PostArliDocuments operation middleware
func (siw *ServerInterfaceWrapper) PostArliDocuments(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostArliDocuments(c)
}

// PostArliDocumentsVectorize operation middleware
func (siw *ServerInterfaceWrapper) PostArliDocumentsVectorize(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostArliDocumentsVectorize(c)
}

// DeleteArliDocumentsId operation middleware
func (siw *ServerInterfaceWrapper) DeleteArliDocumentsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteArliDocumentsId(c, id)
}

// GetArliDocumentsId operation middleware
func (siw *ServerInterfaceWrapper) GetArliDocumentsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetArliDocumentsId(c, id)
}

// PutArliDocumentsId operation middleware
func (siw *ServerInterfaceWrapper) PutArliDocumentsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutArliDocumentsId(c, id)
}

// GetArliFilterTags operation middleware
func (siw *ServerInterfaceWrapper) GetArliFilterTags(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetArliFilterTags(c)
}

// PostArliFilterTags operation middleware
func (siw *ServerInterfaceWrapper) PostArliFilterTags(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostArliFilterTags(c)
}

// GetAuthCallbackProvider operation middleware
func (siw *ServerInterfaceWrapper) GetAuthCallbackProvider(c *gin.Context) {

	var err error

	// ------------- Path parameter "provider" -------------
	var provider GetAuthCallbackProviderParamsProvider

	err = runtime.BindStyledParameterWithOptions("simple", "provider", c.Param("provider"), &provider, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter provider: %w", err), http.StatusBadRequest)
		return
	}

	// Parameter object where we will unmarshal all parameters from the context
	var params GetAuthCallbackProviderParams

	// ------------- Required query parameter "code" -------------

	if paramValue := c.Query("code"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument code is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "code", c.Request.URL.Query(), &params.Code)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter code: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Required query parameter "state" -------------

	if paramValue := c.Query("state"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument state is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "state", c.Request.URL.Query(), &params.State)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter state: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "error" -------------

	err = runtime.BindQueryParameter("form", true, false, "error", c.Request.URL.Query(), &params.Error)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter error: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAuthCallbackProvider(c, provider, params)
}

// GetAuthSession operation middleware
func (siw *ServerInterfaceWrapper) GetAuthSession(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAuthSession(c)
}

// PostAuthSignin operation middleware
func (siw *ServerInterfaceWrapper) PostAuthSignin(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAuthSignin(c)
}

// PostAuthSigninOauth operation middleware
func (siw *ServerInterfaceWrapper) PostAuthSigninOauth(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAuthSigninOauth(c)
}

// PostAuthSignout operation middleware
func (siw *ServerInterfaceWrapper) PostAuthSignout(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAuthSignout(c)
}

// GetAuthUser operation middleware
func (siw *ServerInterfaceWrapper) GetAuthUser(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAuthUser(c)
}

// GetCompanies operation middleware
func (siw *ServerInterfaceWrapper) GetCompanies(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetCompaniesParams

	// ------------- Optional query parameter "status_id" -------------

	err = runtime.BindQueryParameter("form", true, false, "status_id", c.Request.URL.Query(), &params.StatusId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter status_id: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "include_deleted" -------------

	err = runtime.BindQueryParameter("form", true, false, "include_deleted", c.Request.URL.Query(), &params.IncludeDeleted)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter include_deleted: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetCompanies(c, params)
}

// PostCompanies operation middleware
func (siw *ServerInterfaceWrapper) PostCompanies(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostCompanies(c)
}

// DeleteCompaniesId operation middleware
func (siw *ServerInterfaceWrapper) DeleteCompaniesId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteCompaniesId(c, id)
}

// GetCompaniesId operation middleware
func (siw *ServerInterfaceWrapper) GetCompaniesId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetCompaniesId(c, id)
}

// PutCompaniesId operation middleware
func (siw *ServerInterfaceWrapper) PutCompaniesId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutCompaniesId(c, id)
}

// PutCompaniesIdStatus operation middleware
func (siw *ServerInterfaceWrapper) PutCompaniesIdStatus(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutCompaniesIdStatus(c, id)
}

// GetCompanyStatuses operation middleware
func (siw *ServerInterfaceWrapper) GetCompanyStatuses(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetCompanyStatuses(c)
}

// GetContacts operation middleware
func (siw *ServerInterfaceWrapper) GetContacts(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetContactsParams

	// ------------- Optional query parameter "company_id" -------------

	err = runtime.BindQueryParameter("form", true, false, "company_id", c.Request.URL.Query(), &params.CompanyId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter company_id: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "unlinked" -------------

	err = runtime.BindQueryParameter("form", true, false, "unlinked", c.Request.URL.Query(), &params.Unlinked)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter unlinked: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "include_deleted" -------------

	err = runtime.BindQueryParameter("form", true, false, "include_deleted", c.Request.URL.Query(), &params.IncludeDeleted)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter include_deleted: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetContacts(c, params)
}

// PostContacts operation middleware
func (siw *ServerInterfaceWrapper) PostContacts(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostContacts(c)
}

// DeleteContactsId operation middleware
func (siw *ServerInterfaceWrapper) DeleteContactsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteContactsId(c, id)
}

// GetContactsId operation middleware
func (siw *ServerInterfaceWrapper) GetContactsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetContactsId(c, id)
}

// PutContactsId operation middleware
func (siw *ServerInterfaceWrapper) PutContactsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutContactsId(c, id)
}

// DeleteContactsIdCompany operation middleware
func (siw *ServerInterfaceWrapper) DeleteContactsIdCompany(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteContactsIdCompany(c, id)
}

// PutContactsIdCompany operation middleware
func (siw *ServerInterfaceWrapper) PutContactsIdCompany(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutContactsIdCompany(c, id)
}

// GetDealStages operation middleware
func (siw *ServerInterfaceWrapper) GetDealStages(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetDealStages(c)
}

// GetDeals operation middleware
func (siw *ServerInterfaceWrapper) GetDeals(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetDealsParams

	// ------------- Optional query parameter "stage_id" -------------

	err = runtime.BindQueryParameter("form", true, false, "stage_id", c.Request.URL.Query(), &params.StageId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter stage_id: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "company_id" -------------

	err = runtime.BindQueryParameter("form", true, false, "company_id", c.Request.URL.Query(), &params.CompanyId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter company_id: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetDeals(c, params)
}

// PostDeals operation middleware
func (siw *ServerInterfaceWrapper) PostDeals(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostDeals(c)
}

// DeleteDealsId operation middleware
func (siw *ServerInterfaceWrapper) DeleteDealsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteDealsId(c, id)
}

// GetDealsId operation middleware
func (siw *ServerInterfaceWrapper) GetDealsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetDealsId(c, id)
}

// PutDealsId operation middleware
func (siw *ServerInterfaceWrapper) PutDealsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutDealsId(c, id)
}

// GetInteractions operation middleware
func (siw *ServerInterfaceWrapper) GetInteractions(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetInteractionsParams

	// ------------- Optional query parameter "company_id" -------------

	err = runtime.BindQueryParameter("form", true, false, "company_id", c.Request.URL.Query(), &params.CompanyId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter company_id: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "contact_id" -------------

	err = runtime.BindQueryParameter("form", true, false, "contact_id", c.Request.URL.Query(), &params.ContactId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter contact_id: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "interaction_type" -------------

	err = runtime.BindQueryParameter("form", true, false, "interaction_type", c.Request.URL.Query(), &params.InteractionType)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter interaction_type: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "from_date" -------------

	err = runtime.BindQueryParameter("form", true, false, "from_date", c.Request.URL.Query(), &params.FromDate)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter from_date: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "to_date" -------------

	err = runtime.BindQueryParameter("form", true, false, "to_date", c.Request.URL.Query(), &params.ToDate)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter to_date: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetInteractions(c, params)
}

// PostInteractions operation middleware
func (siw *ServerInterfaceWrapper) PostInteractions(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostInteractions(c)
}

// DeleteInteractionsId operation middleware
func (siw *ServerInterfaceWrapper) DeleteInteractionsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteInteractionsId(c, id)
}

// GetInteractionsId operation middleware
func (siw *ServerInterfaceWrapper) GetInteractionsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetInteractionsId(c, id)
}

// PutInteractionsId operation middleware
func (siw *ServerInterfaceWrapper) PutInteractionsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutInteractionsId(c, id)
}

// GetUsersProfile operation middleware
func (siw *ServerInterfaceWrapper) GetUsersProfile(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUsersProfile(c)
}

// PutUsersProfile operation middleware
func (siw *ServerInterfaceWrapper) PutUsersProfile(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutUsersProfile(c)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/arli/documents", wrapper.GetArliDocuments)
	router.POST(options.BaseURL+"/arli/documents", wrapper.PostArliDocuments)
	router.POST(options.BaseURL+"/arli/documents/vectorize", wrapper.PostArliDocumentsVectorize)
	router.DELETE(options.BaseURL+"/arli/documents/:id", wrapper.DeleteArliDocumentsId)
	router.GET(options.BaseURL+"/arli/documents/:id", wrapper.GetArliDocumentsId)
	router.PUT(options.BaseURL+"/arli/documents/:id", wrapper.PutArliDocumentsId)
	router.GET(options.BaseURL+"/arli/filter-tags", wrapper.GetArliFilterTags)
	router.POST(options.BaseURL+"/arli/filter-tags", wrapper.PostArliFilterTags)
	router.GET(options.BaseURL+"/auth/callback/:provider", wrapper.GetAuthCallbackProvider)
	router.GET(options.BaseURL+"/auth/session", wrapper.GetAuthSession)
	router.POST(options.BaseURL+"/auth/signin", wrapper.PostAuthSignin)
	router.POST(options.BaseURL+"/auth/signin/oauth", wrapper.PostAuthSigninOauth)
	router.POST(options.BaseURL+"/auth/signout", wrapper.PostAuthSignout)
	router.GET(options.BaseURL+"/auth/user", wrapper.GetAuthUser)
	router.GET(options.BaseURL+"/companies", wrapper.GetCompanies)
	router.POST(options.BaseURL+"/companies", wrapper.PostCompanies)
	router.DELETE(options.BaseURL+"/companies/:id", wrapper.DeleteCompaniesId)
	router.GET(options.BaseURL+"/companies/:id", wrapper.GetCompaniesId)
	router.PUT(options.BaseURL+"/companies/:id", wrapper.PutCompaniesId)
	router.PUT(options.BaseURL+"/companies/:id/status", wrapper.PutCompaniesIdStatus)
	router.GET(options.BaseURL+"/company-statuses", wrapper.GetCompanyStatuses)
	router.GET(options.BaseURL+"/contacts", wrapper.GetContacts)
	router.POST(options.BaseURL+"/contacts", wrapper.PostContacts)
	router.DELETE(options.BaseURL+"/contacts/:id", wrapper.DeleteContactsId)
	router.GET(options.BaseURL+"/contacts/:id", wrapper.GetContactsId)
	router.PUT(options.BaseURL+"/contacts/:id", wrapper.PutContactsId)
	router.DELETE(options.BaseURL+"/contacts/:id/company", wrapper.DeleteContactsIdCompany)
	router.PUT(options.BaseURL+"/contacts/:id/company", wrapper.PutContactsIdCompany)
	router.GET(options.BaseURL+"/deal-stages", wrapper.GetDealStages)
	router.GET(options.BaseURL+"/deals", wrapper.GetDeals)
	router.POST(options.BaseURL+"/deals", wrapper.PostDeals)
	router.DELETE(options.BaseURL+"/deals/:id", wrapper.DeleteDealsId)
	router.GET(options.BaseURL+"/deals/:id", wrapper.GetDealsId)
	router.PUT(options.BaseURL+"/deals/:id", wrapper.PutDealsId)
	router.GET(options.BaseURL+"/interactions", wrapper.GetInteractions)
	router.POST(options.BaseURL+"/interactions", wrapper.PostInteractions)
	router.DELETE(options.BaseURL+"/interactions/:id", wrapper.DeleteInteractionsId)
	router.GET(options.BaseURL+"/interactions/:id", wrapper.GetInteractionsId)
	router.PUT(options.BaseURL+"/interactions/:id", wrapper.PutInteractionsId)
	router.GET(options.BaseURL+"/users/profile", wrapper.GetUsersProfile)
	router.PUT(options.BaseURL+"/users/profile", wrapper.PutUsersProfile)
}
