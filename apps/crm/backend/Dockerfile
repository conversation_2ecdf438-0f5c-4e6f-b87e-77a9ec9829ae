FROM golang:1.24-alpine AS builder

WORKDIR /app

# Copy go mod files from root
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY go_lib/ ./go_lib/
COPY platform/crm_backend/ ./platform/crm_backend/

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o crm_backend ./platform/crm_backend

FROM alpine:latest

RUN apk --no-cache add ca-certificates wget
WORKDIR /root/

# Copy the binary from builder
COPY --from=builder /app/crm_backend .

# Copy database migrations (if running migrations from the app)
COPY platform/db/migrations ./db/migrations/

EXPOSE 8003

CMD ["./crm_backend"]