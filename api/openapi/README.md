# CRM REST API Specification

This directory contains the REST API specification for the CRM web application, derived from the existing Supabase database operations.

## Overview

The CRM system uses the following core entities:
- **Companies**: Business entities and prospects
- **Contacts**: Individual people associated with companies
- **Deals**: Sales opportunities and pipeline management
- **Interactions**: Communication history and touchpoints
- **Authentication**: User management and access control
- **Arli**: AI-powered document processing system

## API Structure

The API follows RESTful conventions with the following base patterns:

- `GET /api/v1/{resource}` - List resources
- `GET /api/v1/{resource}/{id}` - Get single resource
- `POST /api/v1/{resource}` - Create new resource
- `PUT /api/v1/{resource}/{id}` - Update resource
- `DELETE /api/v1/{resource}/{id}` - Delete resource

## Authentication

All API endpoints require authentication via JWT tokens obtained through the `/auth` endpoints.

## Files

- `auth.yaml` - Authentication and user management endpoints
- `companies.yaml` - Company management endpoints
- `contacts.yaml` - Contact management endpoints
- `deals.yaml` - Deal pipeline management endpoints
- `interactions.yaml` - Communication tracking endpoints
- `arli.yaml` - AI document processing endpoints
- `schemas.yaml` - Common data schemas and models
- `openapi.yaml` - Complete OpenAPI 3.0 specification

## Usage

These specifications can be used to:
1. Generate API documentation
2. Generate client SDKs
3. Validate API requests/responses
4. Mock API servers for testing
5. Generate server stubs for implementation