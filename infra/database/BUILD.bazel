# Database build rules for Bazel

# Shell script targets for database operations
sh_binary(
    name = "start",
    srcs = ["//infra/database/scripts:start.sh"],
    data = [
        "Dockerfile",
        "docker-compose.yml",
    ] + glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "stop",
    srcs = ["//infra/database/scripts:stop.sh"],
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "migrate",
    srcs = ["//infra/database/scripts:migrate.sh"],
    data = [
        "flyway.conf",
    ] + glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "clean",
    srcs = ["//infra/database/scripts:clean.sh"],
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "clear_data",
    srcs = ["//infra/database/scripts:clear_data.sh"],
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "status",
    srcs = ["//infra/database/scripts:status.sh"],
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "test_data",
    srcs = ["//infra/database/scripts:simple_test_data.sh"],
    visibility = ["//visibility:public"],
)

# Getting started script - complete fresh setup
sh_binary(
    name = "getting_started",
    srcs = ["//infra/database/scripts:getting_started.sh"],
    data = [
        "docker-compose.yml",
        "flyway.conf",
        ":clear_data",
        ":migrate",
        ":start",
        ":test_data",
    ] + glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)

# Nuclear option - remove everything
sh_binary(
    name = "nuke",
    srcs = ["//infra/database/scripts:nuke.sh"],
    visibility = ["//visibility:public"],
)

# Test database setup and teardown
sh_binary(
    name = "setup_test_db",
    srcs = ["//infra/database/scripts:setup_test_db.sh"],
    data = [
        "docker-compose.yml",
        "flyway.conf",
    ] + glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "cleanup_test_db",
    srcs = ["//infra/database/scripts:cleanup_test_db.sh"],
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "fix_flyway_state",
    srcs = ["//infra/database/scripts:fix_flyway_state.sh"],
    visibility = ["//visibility:public"],
)

# GCP Cloud SQL migration scripts
sh_binary(
    name = "migrate_gcp_dev",
    srcs = ["//infra/database/scripts:migrate_gcp_dev.sh"],
    data = [
        "flyway.conf",
    ] + glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "status_gcp_dev",
    srcs = ["//infra/database/scripts:status_gcp_dev.sh"],
    data = [
        "flyway.conf",
    ] + glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)

# Remote GCP migration (runs on compute instance)
sh_binary(
    name = "migrate_gcp_dev_remote",
    srcs = ["//infra/database/scripts:migrate_gcp_dev_remote.sh"],
    data = [
        "flyway.conf",
    ] + glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)

# Simple GCP migration (inline approach)
sh_binary(
    name = "migrate_gcp_dev_simple",
    srcs = ["//infra/database/scripts:migrate_gcp_dev_simple.sh"],
    visibility = ["//visibility:public"],
)

# GCP dev test data insertion
sh_binary(
    name = "test_data_gcp_dev",
    srcs = ["//infra/database/scripts:test_data_gcp_dev.sh"],
    visibility = ["//visibility:public"],
)

# Auto-migration system
sh_binary(
    name = "auto_migrate",
    srcs = ["//infra/database/scripts:auto_migrate.sh"],
    data = [
        "flyway.conf",
        ":migrate",
        ":migrate_gcp_dev_simple",
    ] + glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)

# Migration watcher service
sh_binary(
    name = "migration_watcher",
    srcs = ["//infra/database/scripts:migration_watcher.sh"],
    data = [
        ":auto_migrate",
    ],
    visibility = ["//visibility:public"],
)

# Pre-commit migration hook
sh_binary(
    name = "pre_commit_migration",
    srcs = ["//infra/database/scripts:pre_commit_migration.sh"],
    data = [
        ":auto_migrate",
    ],
    visibility = ["//visibility:public"],
)

# Docker-based database operations (compatible with gateway)
genrule(
    name = "start_docker",
    outs = ["docker_db_started.txt"],
    cmd = """
    docker-compose -f docker-compose.dev.yml up -d postgres && \\
    echo "Docker PostgreSQL started successfully" > $(location docker_db_started.txt)
    """,
    visibility = ["//visibility:public"],
)

genrule(
    name = "stop_docker",
    outs = ["docker_db_stopped.txt"],
    cmd = """
    docker-compose -f docker-compose.dev.yml stop postgres && \\
    echo "Docker PostgreSQL stopped successfully" > $(location docker_db_stopped.txt)
    """,
    visibility = ["//visibility:public"],
)

genrule(
    name = "migrate_docker",
    outs = ["docker_db_migrated.txt"],
    cmd = """
    echo "Waiting for database to be ready..." > $(location docker_db_migrated.txt) && \\
    sleep 5 && \\
    PGPASSWORD=orbit_password psql -h localhost -U orbit_user -d orbit -f infra/database/test_data.sql && \\
    echo "Docker database migrated and seeded successfully" >> $(location docker_db_migrated.txt) && \\
    cat $(location docker_db_migrated.txt)
    """,
    visibility = ["//visibility:public"],
)

genrule(
    name = "status_docker",
    outs = ["docker_db_status.txt"],
    cmd = """
    echo "=== Docker Database Status ===" > $(location docker_db_status.txt) && \\
    docker-compose -f docker-compose.dev.yml ps postgres >> $(location docker_db_status.txt) 2>/dev/null || echo "No Docker database running" >> $(location docker_db_status.txt) && \\
    echo "" >> $(location docker_db_status.txt) && \\
    echo "=== Database Connection Test ===" >> $(location docker_db_status.txt) && \\
    PGPASSWORD=orbit_password psql -h localhost -U orbit_user -d orbit -c "SELECT 'Database connected successfully' as status;" >> $(location docker_db_status.txt) 2>/dev/null || echo "Database connection failed" >> $(location docker_db_status.txt) && \\
    cat $(location docker_db_status.txt)
    """,
    visibility = ["//visibility:public"],
)

# Export test data files for use in other targets
exports_files([
    "test_data.sql",
    "backup_seed_data.sql",
    "container_seed_data.sql",
    "comprehensive_seed_data.sql",
])

# Group all database operations
filegroup(
    name = "migrations",
    srcs = glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)
