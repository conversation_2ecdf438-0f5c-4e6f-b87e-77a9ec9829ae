package handlers

import (
	"database/sql"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/TwoDotAi/orbit/apps/crm/backend/database"
	openapi "github.com/TwoDotAi/orbit/api/openapi"
)

// Use generated OpenAPI types
type UserProfile = openapi.UserProfile
type UserProfileUpdate = openapi.UserProfileUpdate

func GetUserProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	db := database.GetDB()
	var profile UserProfile
	var id, fullName, avatarUrl, timezone string
	var createdAt, updatedAt time.Time
	
	err := db.QueryRow(
		"SELECT id, full_name, avatar_url, timezone, created_at, updated_at FROM user_profiles WHERE id = $1",
		userID,
	).Scan(&id, &fullName, &avatarUrl, &timezone, &createdAt, &updatedAt)
	
	if err == nil {
		// Convert to pointer types
		profile.Id = uuidPtr(id)
		profile.FullName = stringPtr(fullName)
		profile.AvatarUrl = stringPtr(avatarUrl)
		profile.Timezone = stringPtr(timezone)
		profile.CreatedAt = timePtr(createdAt)
		profile.UpdatedAt = timePtr(updatedAt)
	}

	if err == sql.ErrNoRows {
		// Create a default profile if none exists
		now := time.Now()
		profile = UserProfile{
			Id:        uuidPtr(userID.(string)),
			CreatedAt: timePtr(now),
			UpdatedAt: timePtr(now),
		}
		
		_, err = db.Exec(
			"INSERT INTO user_profiles (id, created_at, updated_at) VALUES ($1, $2, $3)",
			userID.(string), now, now,
		)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create profile"})
			return
		}
	} else if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}

	c.JSON(http.StatusOK, profile)
}

func UpdateUserProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req UserProfileUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	db := database.GetDB()
	
	// Update the profile
	_, err := db.Exec(
		`UPDATE user_profiles 
		 SET full_name = $2, avatar_url = $3, timezone = $4, updated_at = $5 
		 WHERE id = $1`,
		userID, req.FullName, req.AvatarUrl, req.Timezone, time.Now(),
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update profile"})
		return
	}

	// Return updated profile
	GetUserProfile(c)
}