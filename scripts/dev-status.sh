#!/bin/bash

# Development Environment Status Script
# Check the status of all services

# Set up Bazel runfiles
if [[ -n "${RUNFILES_DIR:-}" ]]; then
  # Running under Bazel
  cd "$RUNFILES_DIR/_main"
else
  # Running directly
  cd "$(dirname "$0")/.."
fi

echo "=== Orbit Development Environment Status ==="
echo ""

echo "Docker Services:"
docker-compose -f docker-compose.dev.yml ps 2>/dev/null || echo "No development services running"
echo ""

echo "Port Status:"
echo "Gateway (8080): $(curl -s http://localhost:8080/health 2>/dev/null || echo 'Not responding')"

# Check database
if PGPASSWORD=orbit_password psql -h localhost -p 5432 -U orbit_user -d orbit -c "SELECT 'Connected' as status;" > /dev/null 2>&1; then
    echo "Database (5432): Connected"
else
    echo "Database (5432): Not responding"
fi

# Check local services
echo "CRM Web (8081): $(curl -I -s http://localhost:8081 2>/dev/null | head -1 || echo 'Not responding')"
echo "ProjectHub Web (8082): $(curl -I -s http://localhost:8082 2>/dev/null | head -1 || echo 'Not responding')"
echo "ProjectHub API (8003): $(curl -I -s http://localhost:8003/health 2>/dev/null | head -1 || echo 'Not responding')"
echo "Auth Service (8004): $(curl -I -s http://localhost:8004/health 2>/dev/null | head -1 || echo 'Not responding')"
echo "CRM API (8005): $(curl -I -s http://localhost:8005/health 2>/dev/null | head -1 || echo 'Not responding')"

echo ""
echo "Gateway Routes Test:"
echo "CRM route: $(curl -I -s http://localhost:8080/crm/ 2>/dev/null | head -1 || echo 'Failed')"
echo "ProjectHub route: $(curl -I -s http://localhost:8080/hub/ 2>/dev/null | head -1 || echo 'Failed')"
echo "ProjectHub logo: $(curl -I -s http://localhost:8080/hub/two-dot-ai-logo.png 2>/dev/null | head -1 || echo 'Failed')"