#!/bin/bash
# Clean script to remove generated files

# Use BUILD_WORKSPACE_DIRECTORY if available (when run with 'bazel run')
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    WORKSPACE_DIR="$BUILD_WORKSPACE_DIRECTORY"
else
    # Fallback to current directory
    WORKSPACE_DIR="$(pwd)"
fi

TARGET_DIR="$WORKSPACE_DIR/platform/rest-api/generated"

echo "Workspace directory: $WORKSPACE_DIR"
echo "Target directory: $TARGET_DIR"

if [ -d "$TARGET_DIR" ]; then
    rm -rf "$TARGET_DIR"
    echo "Generated files removed from $TARGET_DIR"
else
    echo "No generated directory found at $TARGET_DIR"
fi