package config

import (
	"os"
	
	"github.com/TwoDotAi/orbit/libs/go/logging"
)

type Config struct {
	GatewayPort  string
	Services     map[string]ServiceConfig
	Environment  string
	LogLevel     string
	ServiceRegistry map[string]string // Dynamic service discovery
}

type ServiceConfig struct {
	BaseURL string
	Port    string
	Timeout int // timeout in seconds
}

func Load() *Config {
	cfg := &Config{
		GatewayPort:     getEnv("GATEWAY_PORT", "8080"),
		Environment:     getEnv("ENVIRONMENT", "development"),
		LogLevel:        getEnv("LOG_LEVEL", "info"),
		Services:        make(map[string]ServiceConfig),
		ServiceRegistry: make(map[string]string),
	}

	// Configure services with fixed ports
	// CRM Backend Service
	cfg.Services["crm_backend"] = ServiceConfig{
		BaseURL: getEnv("CRM_BACKEND_URL", "http://localhost:8005"),
		Port:    getEnv("CRM_BACKEND_PORT", "8005"),
		Timeout: 30,
	}

	// Auth Service
	cfg.Services["auth"] = ServiceConfig{
		BaseURL: getEnv("AUTH_SERVICE_URL", "http://localhost:8004"),
		Port:    getEnv("AUTH_SERVICE_PORT", "8004"),
		Timeout: 30,
	}

	// CRM Web Frontend
	cfg.Services["crm_web"] = ServiceConfig{
		BaseURL: getEnv("CRM_WEB_URL", "http://localhost:8081"),
		Port:    getEnv("CRM_WEB_PORT", "8081"),
		Timeout: 30,
	}

	// ProjectHub Backend
	cfg.Services["projecthub_backend"] = ServiceConfig{
		BaseURL: getEnv("PROJECTHUB_BACKEND_URL", "http://localhost:8003"),
		Port:    getEnv("PROJECTHUB_BACKEND_PORT", "8003"),
		Timeout: 30,
	}

	// ProjectHub Frontend
	cfg.Services["projecthub_web"] = ServiceConfig{
		BaseURL: getEnv("PROJECTHUB_WEB_URL", "http://localhost:8082"),
		Port:    getEnv("PROJECTHUB_WEB_PORT", "8082"),
		Timeout: 30,
	}

	logging.ConfigMessage("GATEWAY", cfg.Environment)
	return cfg
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}