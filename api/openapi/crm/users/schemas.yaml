UserProfile:
  type: object
  properties:
    id:
      type: string
      format: uuid
    full_name:
      type: string
    avatar_url:
      type: string
      format: uri
    timezone:
      type: string
    created_at:
      type: string
      format: date-time
    updated_at:
      type: string
      format: date-time

UserProfileUpdate:
  type: object
  properties:
    full_name:
      type: string
    avatar_url:
      type: string
      format: uri
    timezone:
      type: string