package router

import (
	"net/http"
	
	"github.com/gin-gonic/gin"
	"github.com/TwoDotAi/orbit/services/gateway/config"
	"github.com/TwoDotAi/orbit/services/gateway/handlers"
)

func SetupRoutes(r *gin.Engine, cfg *config.Config) {
	proxyHandler := handlers.NewProxyHandler(cfg)

	// API routes - support both /api/v1 and /api patterns
	// CRM API v1 routes
	v1 := r.Group("/api/v1")
	{
		// Authentication routes - handled by auth service
		auth := v1.Group("/auth")
		{
			auth.Any("/*path", proxyHandler.ProxyToService("auth"))
		}

		// User routes - currently handled by crm_backend
		users := v1.Group("/users")
		{
			users.Any("/*path", proxyHandler.ProxyToService("crm_backend"))
		}

		// Company routes - routed to crm_backend
		companies := v1.Group("/companies")
		{
			companies.Any("", proxyHandler.ProxyToService("crm_backend"))
			companies.Any("/*path", proxyHandler.ProxyToService("crm_backend"))
		}

		// Company statuses - routed to crm_backend
		v1.Any("/company-statuses", proxyHandler.ProxyToService("crm_backend"))

		// Contact routes - routed to crm_backend
		contacts := v1.Group("/contacts")
		{
			contacts.Any("", proxyHandler.ProxyToService("crm_backend"))
			contacts.Any("/*path", proxyHandler.ProxyToService("crm_backend"))
		}

		// Deal routes - routed to crm_backend
		deals := v1.Group("/deals")
		{
			deals.Any("", proxyHandler.ProxyToService("crm_backend"))
			deals.Any("/*path", proxyHandler.ProxyToService("crm_backend"))
		}

		// Deal stages - routed to crm_backend
		v1.Any("/deal-stages", proxyHandler.ProxyToService("crm_backend"))

		// Interaction routes - routed to crm_backend
		interactions := v1.Group("/interactions")
		{
			interactions.Any("", proxyHandler.ProxyToService("crm_backend"))
			interactions.Any("/*path", proxyHandler.ProxyToService("crm_backend"))
		}

		// Arli AI routes - routed to crm_backend
		arli := v1.Group("/arli")
		{
			arli.Any("", proxyHandler.ProxyToService("crm_backend"))
			arli.Any("/*path", proxyHandler.ProxyToService("crm_backend"))
		}
	}

	// ProjectHub API routes - using /api pattern (not /api/v1)
	api := r.Group("/api")
	{
		// Project routes - routed to projecthub_backend
		projectRoutes := api.Group("/projects")
		{
			projectRoutes.Any("", proxyHandler.ProxyToService("projecthub_backend"))
			projectRoutes.Any("/*path", proxyHandler.ProxyToService("projecthub_backend"))
		}

		// Team members routes - routed to projecthub_backend
		teamRoutes := api.Group("/team-members")
		{
			teamRoutes.Any("", proxyHandler.ProxyToService("projecthub_backend"))
			teamRoutes.Any("/*path", proxyHandler.ProxyToService("projecthub_backend"))
		}

		// Impact types routes - routed to projecthub_backend
		impactRoutes := api.Group("/impact-types")
		{
			impactRoutes.Any("", proxyHandler.ProxyToService("projecthub_backend"))
			impactRoutes.Any("/*path", proxyHandler.ProxyToService("projecthub_backend"))
		}

		// Auth routes for ProjectHub - routed to projecthub_backend
		authRoutes := api.Group("/auth")
		{
			authRoutes.Any("", proxyHandler.ProxyToService("projecthub_backend"))
			authRoutes.Any("/*path", proxyHandler.ProxyToService("projecthub_backend"))
		}

		// Admin routes for ProjectHub - routed to projecthub_backend
		adminRoutes := api.Group("/admin")
		{
			adminRoutes.Any("", proxyHandler.ProxyToService("projecthub_backend"))
			adminRoutes.Any("/*path", proxyHandler.ProxyToService("projecthub_backend"))
		}

		// Requirements routes for ProjectHub - routed to projecthub_backend
		requirementsRoutes := api.Group("/requirements")
		{
			requirementsRoutes.Any("", proxyHandler.ProxyToService("projecthub_backend"))
			requirementsRoutes.Any("/*path", proxyHandler.ProxyToService("projecthub_backend"))
		}

		// AI Analysis routes for ProjectHub - routed to projecthub_backend
		aiRoutes := api.Group("/ai-analysis")
		{
			aiRoutes.Any("", proxyHandler.ProxyToService("projecthub_backend"))
			aiRoutes.Any("/*path", proxyHandler.ProxyToService("projecthub_backend"))
		}
	}

	// CRM Web App - /crm/* 
	crm := r.Group("/crm")
	{
		// All CRM routes - no wildcards, just proxy everything to crm_web
		crm.Any("", proxyHandler.ProxyToService("crm_web"))
		crm.Any("/*path", proxyHandler.ProxyToService("crm_web"))
	}
	
	// Root redirect to CRM (but handle WebSocket HMR connections for Vite)
	r.GET("/", func(c *gin.Context) {
		// Check if this is a Vite HMR WebSocket connection
		if c.Query("token") != "" || c.GetHeader("Upgrade") == "websocket" {
			proxyHandler.ProxyToService("projecthub_web")(c)
			return
		}
		c.Redirect(http.StatusMovedPermanently, "/crm")
	})

	// Vite development server assets for ProjectHub (when accessed via /hub/)
	// These must be registered BEFORE /hub/* to avoid conflicts
	r.Any("/@vite/*path", proxyHandler.ProxyToService("projecthub_web"))
	r.Any("/@react-refresh", proxyHandler.ProxyToService("projecthub_web"))
	r.Any("/src/*path", proxyHandler.ProxyToService("projecthub_web"))
	r.Any("/@fs/*path", proxyHandler.ProxyToService("projecthub_web"))
	r.Any("/node_modules/*path", proxyHandler.ProxyToService("projecthub_web"))
	
	// Vite HMR WebSocket connections
	r.GET("/ws", proxyHandler.ProxyToService("projecthub_web"))

	// ProjectHub Web App - /hub/*
	hub := r.Group("/hub")
	{
		hub.Any("", proxyHandler.ProxyToService("projecthub_web"))
		hub.Any("/*path", proxyHandler.ProxyToService("projecthub_web"))
	}

}