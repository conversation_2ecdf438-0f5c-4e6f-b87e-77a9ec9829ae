# Orbit CRM Monorepo - Git Ignore File

###################
# Bazel artifacts #
###################
bazel-*
.bazel/
.bazelrc.user

###################
# Build outputs   #
###################
dist/
build/
target/
out/

###################
# Dependencies    #
###################
node_modules/
.pnp
.pnp.js

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
pip-log.txt
pip-delete-this-directory.txt

# Go
vendor/
*.test
*.out

# Java
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

###################
# IDE files       #
###################
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/

###################
# OS files        #
###################
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

###################
# Logs            #
###################
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Application logs
auth.log
auth_*.log
crm_*.log
project_hub*.log
backend.log
frontend.log
gateway.log

###################
# Databases       #
###################
*.db
*.sqlite
*.sqlite3

# Database backups
*backup*.sql
*/backups/
*/backup/

###################
# Environment     #
###################
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

###################
# Docker          #
###################
.dockerignore

###################
# Frontend        #
###################
# Compiled assets
*.css.map
*.js.map

# Coverage directory used by tools like istanbul
coverage/
.nyc_output

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Temporary folders
tmp/
temp/
.tmp/
.temp/

###################
# Testing         #
###################
.coverage
.junit
test-results/
coverage.xml

###################
# Generated files #
###################
# API clients
*/api-client/generated/
*/generated/

# Protobuf generated files
*.pb.go
*_pb2.py
*_pb2_grpc.py

###################
# Certificates    #
###################
*.pem
*.key
*.crt
*.p12
*.pfx

###################
# Secrets         #
###################
secrets/
*.secret
*secret*
.secrets/

###################
# Terraform       #
###################
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl
terraform.tfplan
terraform.tfplan.*

###################
# Cloud configs   #
###################
*gcp-key*.json
*service-account*.json
.gcloud/

###################
# Monitoring      #
###################
*.prof
*.trace

###################
# Cache           #
###################
.cache/
*.cache
.eslintcache

###################
# Lock files      #
###################
# Keep package-lock.json for reproducible builds
# But ignore other lock files that might conflict
yarn.lock
pnpm-lock.yaml
.pnpm-store/

###################
# Project specific#
###################
# Orbit specific temporary files
test_run.log
import.log
migration_log.txt
orbit_backup*.sql

# Zero2Hero temporary directories
/tmp/orbit-zero2hero-*/