Session:
  type: object
  properties:
    access_token:
      type: string
    refresh_token:
      type: string
    expires_in:
      type: integer
    token_type:
      type: string
    user:
      $ref: '#/User'

AuthResponse:
  type: object
  properties:
    access_token:
      type: string
    refresh_token:
      type: string
    expires_in:
      type: integer
    token_type:
      type: string
    user:
      $ref: '#/User'

User:
  type: object
  properties:
    id:
      type: string
      format: uuid
    email:
      type: string
      format: email
    email_confirmed_at:
      type: string
      format: date-time
    created_at:
      type: string
      format: date-time
    updated_at:
      type: string
      format: date-time

ValidationError:
  type: object
  properties:
    code:
      type: string
      example: "validation_error"
    message:
      type: string
      example: "Invalid input data"
    details:
      type: object

ErrorResponse:
  type: object
  properties:
    error:
      type: string
    message:
      type: string