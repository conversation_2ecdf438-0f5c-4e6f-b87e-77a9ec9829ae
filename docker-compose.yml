version: '3.8'

services:
  # Database
  postgres:
    image: postgres:17
    container_name: orbit-postgres
    environment:
      POSTGRES_DB: orbit
      POSTGRES_USER: orbit_user
      POSTGRES_PASSWORD: orbit_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infra/database/backup_seed_data.sql:/docker-entrypoint-initdb.d/backup_seed_data.sql
    networks:
      - orbit-network

  # Gateway (nginx reverse proxy)
  gateway:
    build:
      context: ./gateway
      dockerfile: Dockerfile
    container_name: orbit-gateway
    ports:
      - "8080:8080"
    depends_on:
      - crm-frontend
      - project-hub-frontend
      - crm-backend
      - auth-service
      - project-hub-backend
    networks:
      - orbit-network

  # Auth Service
  auth-service:
    build:
      context: ./services/auth
      dockerfile: Dockerfile
    container_name: orbit-auth-service
    ports:
      - "8004:8004"
    environment:
      - DATABASE_URL=**************************************************/orbit
      - JWT_SECRET=your-jwt-secret-here
    depends_on:
      - postgres
    networks:
      - orbit-network

  # CRM Backend
  crm-backend:
    build:
      context: ./apps/crm/backend
      dockerfile: Dockerfile
    container_name: orbit-crm-backend
    ports:
      - "8005:8005"
    environment:
      - DATABASE_URL=**************************************************/orbit
    depends_on:
      - postgres
    networks:
      - orbit-network

  # CRM Frontend
  crm-frontend:
    build:
      context: ./apps/crm/web
      dockerfile: Dockerfile
    container_name: orbit-crm-frontend
    ports:
      - "8081:8081"
    environment:
      - VITE_API_URL=http://localhost:8080/api/v1
    networks:
      - orbit-network

  # ProjectHub Backend
  project-hub-backend:
    build:
      context: ./apps/project-hub/backend
      dockerfile: Dockerfile
    container_name: orbit-project-hub-backend
    ports:
      - "8003:8003"
    environment:
      - DATABASE_URL=**************************************************/orbit
    depends_on:
      - postgres
    networks:
      - orbit-network

  # ProjectHub Frontend
  project-hub-frontend:
    build:
      context: ./apps/project-hub/frontend
      dockerfile: Dockerfile
    container_name: orbit-project-hub-frontend
    ports:
      - "8082:8082"
    environment:
      - VITE_API_URL=http://localhost:8080/api
    networks:
      - orbit-network

volumes:
  postgres_data:

networks:
  orbit-network:
    driver: bridge