# Interaction paths with named anchors
interactions: &interactions
  get:
    tags: [Interactions]
    summary: List interactions
    description: Retrieve a list of all interactions
    parameters:
      - name: company_id
        in: query
        schema:
          type: string
          format: uuid
        description: Filter by company ID
      - name: contact_id
        in: query
        schema:
          type: string
          format: uuid
        description: Filter by contact ID
      - name: interaction_type
        in: query
        schema:
          type: string
        description: Filter by interaction type
      - name: from_date
        in: query
        schema:
          type: string
          format: date-time
        description: Filter interactions from this date
      - name: to_date
        in: query
        schema:
          type: string
          format: date-time
        description: Filter interactions to this date
      - name: limit
        in: query
        schema:
          type: integer
          minimum: 1
          maximum: 100
          default: 20
        description: Number of interactions to return
      - name: offset
        in: query
        schema:
          type: integer
          minimum: 0
          default: 0
        description: Number of interactions to skip
    responses:
      '200':
        description: List of interactions
        content:
          application/json:
            schema:
              type: object
              properties:
                interactions:
                  type: array
                  items:
                    $ref: './schemas.yaml#/InteractionWithDetails'
                total_count:
                  type: integer
                has_more:
                  type: boolean
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'

  post:
    tags: [Interactions]
    summary: Create interaction
    description: Create a new interaction record
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/InteractionCreate'
    responses:
      '201':
        description: Interaction created successfully
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/Interaction'
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'

interactions-by-id: &interactions-by-id
  get:
    tags: [Interactions]
    summary: Get interaction
    description: Retrieve a specific interaction by ID
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '200':
        description: Interaction details
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/InteractionDetails'
      '404':
        description: Interaction not found
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'

  put:
    tags: [Interactions]
    summary: Update interaction
    description: Update an existing interaction
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/InteractionUpdate'
    responses:
      '200':
        description: Interaction updated successfully
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/Interaction'
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'
      '404':
        description: Interaction not found
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'

  delete:
    tags: [Interactions]
    summary: Delete interaction
    description: Delete an interaction record
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '204':
        description: Interaction deleted successfully
      '404':
        description: Interaction not found
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'