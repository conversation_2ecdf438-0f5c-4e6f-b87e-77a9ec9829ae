# CRM Backend

A comprehensive CRM backend service built with Go and Gin, providing REST API endpoints for customer relationship management.

## Features

- **Authentication**: JWT-based authentication with OAuth support
- **Companies**: Full CRUD operations with status pipeline management
- **Contacts**: Contact management with company linking
- **Deals**: Sales pipeline with stage management
- **Users**: User profile management
- **Database**: PostgreSQL with migrations
- **API Documentation**: OpenAPI 3.0 specification

## API Endpoints

### Authentication
- `GET /api/v1/auth/session` - Get current session
- `POST /api/v1/auth/signin` - Sign in with email/password
- `POST /api/v1/auth/signin/oauth` - OAuth sign in
- `POST /api/v1/auth/signout` - Sign out
- `GET /api/v1/auth/user` - Get current user

### Companies
- `GET /api/v1/companies` - List companies
- `POST /api/v1/companies` - Create company
- `GET /api/v1/companies/:id` - Get company details
- `PUT /api/v1/companies/:id` - Update company
- `DELETE /api/v1/companies/:id` - Delete company (soft delete)
- `PUT /api/v1/companies/:id/status` - Update company status
- `GET /api/v1/company-statuses` - List company statuses

### Contacts
- `GET /api/v1/contacts` - List contacts
- `POST /api/v1/contacts` - Create contact
- `GET /api/v1/contacts/:id` - Get contact details
- `PUT /api/v1/contacts/:id` - Update contact
- `DELETE /api/v1/contacts/:id` - Delete contact (soft delete)
- `PUT /api/v1/contacts/:id/company` - Link contact to company
- `DELETE /api/v1/contacts/:id/company` - Unlink contact from company

### Deals
- `GET /api/v1/deals` - List deals
- `POST /api/v1/deals` - Create deal
- `GET /api/v1/deals/:id` - Get deal details
- `PUT /api/v1/deals/:id` - Update deal
- `DELETE /api/v1/deals/:id` - Delete deal
- `GET /api/v1/deal-stages` - List deal stages

### Users
- `GET /api/v1/users/profile` - Get user profile
- `PUT /api/v1/users/profile` - Update user profile

## Configuration

Environment variables:
- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET` - Secret key for JWT tokens
- `GOOGLE_OAUTH_CLIENT_ID` - Google OAuth client ID
- `ENVIRONMENT` - Environment (development/staging/production)
- `PORT` - Server port (default: 8003)

## Building and Running

### With Bazel
```bash
# Build the service
bazel build //crm_backend:crm_backend

# Run the service
bazel run //crm_backend:crm_backend
```

### With Docker
```bash
# Build Docker image
docker build -t crm-backend .

# Run with Docker
docker run -p 8003:8003 -e DATABASE_URL="postgres://..." crm-backend
```

### Local Development
```bash
# Install dependencies
go mod download

# Run the service
go run main.go
```

## Database Setup

1. Start PostgreSQL database:
```bash
bazel run //db:start
```

2. Run migrations:
```bash
bazel run //db:migrate
```

The migration will create all necessary tables for the CRM system including:
- Users and user profiles
- Companies and company statuses
- Contacts
- Deals and deal stages
- Interactions
- Arli documents (AI processing)

## API Documentation

The API follows the OpenAPI 3.0 specification located at `/platform/rest-api/openapi.yaml`. The specification includes:
- Complete schema definitions
- Request/response examples
- Authentication requirements
- Error handling

## Architecture

- **Handlers**: HTTP request handlers for each entity
- **Middleware**: Authentication, CORS, and error handling
- **Database**: PostgreSQL with connection pooling
- **Config**: Environment-based configuration
- **Migrations**: Database schema versioning

## Security

- JWT-based authentication
- Password hashing with bcrypt
- CORS protection
- Input validation
- SQL injection prevention with parameterized queries

## Testing

```bash
# Run tests
bazel test //crm_backend:all
```