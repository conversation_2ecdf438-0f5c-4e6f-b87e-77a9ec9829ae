# Shared error schemas
Error:
  type: object
  properties:
    code:
      type: string
    message:
      type: string
    details:
      type: object
  required:
    - code
    - message

ValidationError:
  type: object
  properties:
    code:
      type: string
      example: "validation_error"
    message:
      type: string
      example: "Invalid input data"
    details:
      type: object
      title: ValidationErrorDetails
      properties:
        field_errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
  required:
    - code
    - message