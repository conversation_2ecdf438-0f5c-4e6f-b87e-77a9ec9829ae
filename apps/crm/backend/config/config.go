package config

import (
	"os"
)

type Config struct {
	DatabaseURL    string
	JWTSecret      string
	GoogleOAuthID  string
	Environment    string
	Port           string
	AuthServiceURL string
}

func Load() *Config {
	return &Config{
		DatabaseURL:    getEnv("DATABASE_URL", "postgres://postgres:postgres@localhost:5432/appdb?sslmode=disable"),
		JWTSecret:      getEnv("JWT_SECRET", "your-secret-key-change-in-production"),
		GoogleOAuthID:  getEnv("GOOGLE_OAUTH_CLIENT_ID", ""),
		Environment:    getEnv("ENVIRONMENT", "development"),
		Port:           getEnv("PORT", "8003"),
		AuthServiceURL: getEnv("AUTH_SERVICE_URL", "http://localhost:8004"),
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}