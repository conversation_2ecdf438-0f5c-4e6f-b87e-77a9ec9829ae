package handlers

import (
	"database/sql"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/TwoDotAi/orbit/apps/crm/backend/database"
	"github.com/TwoDotAi/orbit/libs/go/logging"
	openapi "github.com/TwoDotAi/orbit/api/openapi"
	"go.uber.org/zap"
)

// Use generated OpenAPI types
type Company = openapi.Company
type CompanyDetails = openapi.Company
type CompanyCreate = openapi.CompanyCreate
type CompanyUpdate = openapi.CompanyUpdate

type CompanyStatus = openapi.CompanyStatus

// ptrToString is now in helpers.go

type ResearchNote struct {
	ID          string    `json:"id"`
	EntityType  string    `json:"entity_type"`
	EntityID    string    `json:"entity_id"`
	NoteContent string    `json:"note_content"`
	CreatedAt   time.Time `json:"created_at"`
}

func ListCompanies(c *gin.Context) {
	db := database.GetDB()
	
	statusID := c.Query("status_id")
	includeDeleted := c.Query("include_deleted") == "true"
	
	query := `SELECT id, name, website, phone, address, notes, company_status_id, is_deleted, created_at, updated_at 
			  FROM companies WHERE 1=1`
	args := []interface{}{}
	argIndex := 1

	if statusID != "" {
		query += " AND company_status_id = $" + string(rune(argIndex+'0'))
		args = append(args, statusID)
		argIndex++
	}

	if !includeDeleted {
		query += " AND is_deleted = false"
	}

	query += " ORDER BY created_at DESC"

	rows, err := db.Query(query, args...)
	if err != nil {
		logging.LogError(err, "Failed to query companies",
			zap.String("operation", "list_companies"),
			zap.String("query", query),
			zap.Any("args", args),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	defer rows.Close()

	var companies []Company
	for rows.Next() {
		var company Company
		var id, name string
		var website, phone, address, notes, companyStatusId sql.NullString
		var isDeleted bool
		var createdAt, updatedAt time.Time
		
		err := rows.Scan(&id, &name, &website, &phone,
			&address, &notes, &companyStatusId, &isDeleted,
			&createdAt, &updatedAt)
		if err != nil {
			logging.LogError(err, "Failed to scan company row",
				zap.String("operation", "list_companies"),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}
		
		// Convert to pointer types for OpenAPI
		company.Id = uuidPtr(id)
		company.Name = stringPtr(name)
		company.Website = nullStringToPtr(website)
		company.Phone = nullStringToPtr(phone)
		company.Address = nullStringToPtr(address)
		company.Notes = nullStringToPtr(notes)
		company.CompanyStatusId = nullStringToUUIDPtr(companyStatusId)
		company.IsDeleted = boolPtr(isDeleted)
		company.CreatedAt = timePtr(createdAt)
		company.UpdatedAt = timePtr(updatedAt)
		
		companies = append(companies, company)
	}

	c.JSON(http.StatusOK, companies)
}

func CreateCompany(c *gin.Context) {
	userIDVal, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	userID, ok := userIDVal.(string)
	if !ok || userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user authentication"})
		return
	}
	
	var req CompanyCreate
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	db := database.GetDB()
	
	id := uuid.New().String()
	now := time.Now()
	
	company := Company{
		Id:              uuidPtr(id),
		Name:            stringPtr(req.Name),
		Website:         req.Website,
		Phone:           req.Phone,
		Address:         req.Address,
		Notes:           req.Notes,
		CompanyStatusId: req.CompanyStatusId,
		IsDeleted:       boolPtr(false),
		CreatedAt:       timePtr(now),
		UpdatedAt:       timePtr(now),
	}

	// Convert pointer to value for database insertion
	var companyStatusId interface{} = nil
	if company.CompanyStatusId != nil {
		companyStatusId = company.CompanyStatusId.String()
	}

	_, err := db.Exec(
		`INSERT INTO companies (id, name, website, phone, address, notes, company_status_id, is_deleted, created_by, created_at, updated_at)
		 VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
		company.Id, company.Name, company.Website, company.Phone, company.Address,
		company.Notes, companyStatusId, company.IsDeleted, userID, company.CreatedAt, company.UpdatedAt,
	)
	if err != nil {
		logging.LogError(err, "Failed to create company",
			zap.String("operation", "create_company"),
			zap.String("company_name", ptrToString(company.Name)),
			zap.String("company_id", ptrToUUID(company.Id)),
			zap.Any("user_id", userID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create company"})
		return
	}

	c.JSON(http.StatusCreated, company)
}

func GetCompany(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Company ID required"})
		return
	}

	db := database.GetDB()
	
	var company Company
	var cid, name string
	var website, phone, address, notes, companyStatusId sql.NullString
	var isDeleted bool
	var createdAt, updatedAt time.Time
	
	err := db.QueryRow(
		`SELECT id, name, website, phone, address, notes, company_status_id, is_deleted, created_at, updated_at
		 FROM companies WHERE id = $1 AND is_deleted = false`,
		id,
	).Scan(&cid, &name, &website, &phone,
		&address, &notes, &companyStatusId, &isDeleted,
		&createdAt, &updatedAt)
	
	if err == sql.ErrNoRows {
		c.JSON(http.StatusNotFound, gin.H{"error": "Company not found"})
		return
	}
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	
	// Convert to pointer types for OpenAPI
	company.Id = uuidPtr(cid)
	company.Name = stringPtr(name)
	company.Website = nullStringToPtr(website)
	company.Phone = nullStringToPtr(phone)
	company.Address = nullStringToPtr(address)
	company.Notes = nullStringToPtr(notes)
	company.CompanyStatusId = nullStringToUUIDPtr(companyStatusId)
	company.IsDeleted = boolPtr(isDeleted)
	company.CreatedAt = timePtr(createdAt)
	company.UpdatedAt = timePtr(updatedAt)

	// TODO: Add contacts when contacts.go is fixed
	c.JSON(http.StatusOK, company)
}

func UpdateCompany(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Company ID required"})
		return
	}

	var req CompanyUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	db := database.GetDB()
	
	// Build dynamic update query
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	if req.Name != nil {
		setParts = append(setParts, "name = $"+string(rune(argIndex+'0')))
		args = append(args, *req.Name)
		argIndex++
	}
	if req.Website != nil {
		setParts = append(setParts, "website = $"+string(rune(argIndex+'0')))
		args = append(args, *req.Website)
		argIndex++
	}
	if req.Phone != nil {
		setParts = append(setParts, "phone = $"+string(rune(argIndex+'0')))
		args = append(args, *req.Phone)
		argIndex++
	}
	if req.Address != nil {
		setParts = append(setParts, "address = $"+string(rune(argIndex+'0')))
		args = append(args, *req.Address)
		argIndex++
	}
	if req.Notes != nil {
		setParts = append(setParts, "notes = $"+string(rune(argIndex+'0')))
		args = append(args, *req.Notes)
		argIndex++
	}
	if req.CompanyStatusId != nil {
		setParts = append(setParts, "company_status_id = $"+string(rune(argIndex+'0')))
		args = append(args, req.CompanyStatusId.String())
		argIndex++
	}

	if len(setParts) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	// Add updated_at
	setParts = append(setParts, "updated_at = $"+string(rune(argIndex+'0')))
	args = append(args, time.Now())
	argIndex++

	// Add WHERE clause
	args = append(args, id)

	query := "UPDATE companies SET " + setParts[0]
	for i := 1; i < len(setParts); i++ {
		query += ", " + setParts[i]
	}
	query += " WHERE id = $" + string(rune(argIndex+'0')) + " AND is_deleted = false"

	result, err := db.Exec(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update company"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Company not found"})
		return
	}

	// Return updated company
	GetCompany(c)
}

func DeleteCompany(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Company ID required"})
		return
	}

	db := database.GetDB()
	
	result, err := db.Exec(
		"UPDATE companies SET is_deleted = true, updated_at = $1 WHERE id = $2 AND is_deleted = false",
		time.Now(), id,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete company"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Company not found"})
		return
	}

	c.Status(http.StatusNoContent)
}

func UpdateCompanyStatus(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Company ID required"})
		return
	}

	var req struct {
		CompanyStatusID string `json:"company_status_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	db := database.GetDB()
	
	result, err := db.Exec(
		"UPDATE companies SET company_status_id = $1, updated_at = $2 WHERE id = $3 AND is_deleted = false",
		req.CompanyStatusID, time.Now(), id,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update company status"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Company not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Company status updated successfully"})
}

func ListCompanyStatuses(c *gin.Context) {
	db := database.GetDB()
	
	rows, err := db.Query(
		"SELECT id, name, pipeline_order, created_at FROM company_statuses ORDER BY pipeline_order",
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	defer rows.Close()

	var statuses []CompanyStatus
	for rows.Next() {
		var status CompanyStatus
		var pipelineOrder int32
		err := rows.Scan(&status.Id, &status.Name, &pipelineOrder, &status.CreatedAt)
		status.PipelineOrder = intPtr(pipelineOrder)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}
		statuses = append(statuses, status)
	}

	c.JSON(http.StatusOK, statuses)
}

func GetCompanyResearchNotes(c *gin.Context) {
	companyID := c.Param("id")
	if companyID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Company ID required"})
		return
	}

	db := database.GetDB()
	
	// First verify the company exists and is not deleted
	var companyExists bool
	err := db.QueryRow(
		"SELECT EXISTS(SELECT 1 FROM companies WHERE id = $1 AND is_deleted = false)",
		companyID,
	).Scan(&companyExists)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	if !companyExists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Company not found"})
		return
	}

	// Get research notes for the company
	rows, err := db.Query(
		`SELECT id, entity_type, entity_id, note_content, created_at 
		 FROM research_notes 
		 WHERE entity_type = 'company' AND entity_id = $1 
		 ORDER BY created_at DESC`,
		companyID,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	defer rows.Close()

	var researchNotes []ResearchNote
	for rows.Next() {
		var note ResearchNote
		err := rows.Scan(&note.ID, &note.EntityType, &note.EntityID, &note.NoteContent, &note.CreatedAt)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}
		researchNotes = append(researchNotes, note)
	}

	c.JSON(http.StatusOK, researchNotes)
}