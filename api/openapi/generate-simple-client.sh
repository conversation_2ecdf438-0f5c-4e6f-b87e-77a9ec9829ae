#!/bin/bash
# Generate a simple TypeScript client manually

set -e

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
WORKSPACE_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
OUTPUT_DIR="$WORKSPACE_ROOT/platform/web/src/api-client"

echo "Creating simple TypeScript API client..."
echo "Output dir: $OUTPUT_DIR"

# Create output directory
mkdir -p "$OUTPUT_DIR"
rm -rf "$OUTPUT_DIR"/*

# Create the main API client class
cat > "$OUTPUT_DIR/client.ts" << 'EOF'
/**
 * Simple CRM API Client
 * Generated client for the CRM REST API
 */

export interface ApiResponse<T> {
  data: T;
  status: number;
  statusText: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

export class ApiClient {
  private baseUrl: string;
  private headers: Record<string, string>;

  constructor(baseUrl: string, token?: string) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.headers = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      this.headers['Authorization'] = `Bearer ${token}`;
    }
  }

  private async request<T>(
    method: string,
    path: string,
    body?: any
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${path}`;
    
    const options: RequestInit = {
      method,
      headers: this.headers,
    };

    if (body) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
      data,
      status: response.status,
      statusText: response.statusText,
    };
  }

  // Auth methods
  async signIn(email: string, password: string) {
    return this.request<any>('POST', '/auth/signin', { email, password });
  }

  async signOut() {
    return this.request<any>('POST', '/auth/signout');
  }

  async getCurrentUser() {
    return this.request<any>('GET', '/auth/user');
  }

  // Company methods
  async getCompanies(params?: { status_id?: string; include_deleted?: boolean }) {
    const query = params ? `?${new URLSearchParams(params as any)}` : '';
    return this.request<any[]>('GET', `/companies${query}`);
  }

  async getCompany(id: string) {
    return this.request<any>('GET', `/companies/${id}`);
  }

  async createCompany(company: any) {
    return this.request<any>('POST', '/companies', company);
  }

  async updateCompany(id: string, company: any) {
    return this.request<any>('PUT', `/companies/${id}`, company);
  }

  async deleteCompany(id: string) {
    return this.request<void>('DELETE', `/companies/${id}`);
  }

  async updateCompanyStatus(id: string, statusId: string) {
    return this.request<void>('PUT', `/companies/${id}/status`, { company_status_id: statusId });
  }

  async getCompanyStatuses() {
    return this.request<any[]>('GET', '/company-statuses');
  }

  // Contact methods
  async getContacts(params?: { company_id?: string; unlinked?: boolean; include_deleted?: boolean }) {
    const query = params ? `?${new URLSearchParams(params as any)}` : '';
    return this.request<any[]>('GET', `/contacts${query}`);
  }

  async getContact(id: string) {
    return this.request<any>('GET', `/contacts/${id}`);
  }

  async createContact(contact: any) {
    return this.request<any>('POST', '/contacts', contact);
  }

  async updateContact(id: string, contact: any) {
    return this.request<any>('PUT', `/contacts/${id}`, contact);
  }

  async deleteContact(id: string) {
    return this.request<void>('DELETE', `/contacts/${id}`);
  }

  async linkContactToCompany(contactId: string, companyId: string) {
    return this.request<void>('PUT', `/contacts/${contactId}/company`, { company_id: companyId });
  }

  async unlinkContactFromCompany(contactId: string) {
    return this.request<void>('DELETE', `/contacts/${contactId}/company`);
  }

  // Deal methods
  async getDeals(params?: { stage_id?: string; company_id?: string }) {
    const query = params ? `?${new URLSearchParams(params as any)}` : '';
    return this.request<any[]>('GET', `/deals${query}`);
  }

  async getDeal(id: string) {
    return this.request<any>('GET', `/deals/${id}`);
  }

  async createDeal(deal: any) {
    return this.request<any>('POST', '/deals', deal);
  }

  async updateDeal(id: string, deal: any) {
    return this.request<any>('PUT', `/deals/${id}`, deal);
  }

  async deleteDeal(id: string) {
    return this.request<void>('DELETE', `/deals/${id}`);
  }

  async getDealStages() {
    return this.request<any[]>('GET', '/deal-stages');
  }

  // User profile methods
  async getUserProfile() {
    return this.request<any>('GET', '/users/profile');
  }

  async updateUserProfile(profile: any) {
    return this.request<any>('PUT', '/users/profile', profile);
  }
}
EOF

# Create TypeScript type definitions
cat > "$OUTPUT_DIR/types.ts" << 'EOF'
/**
 * TypeScript type definitions for CRM API
 */

// Base types
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

// Auth types
export interface User {
  id: string;
  email: string;
  email_confirmed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface UserProfile extends BaseEntity {
  full_name?: string;
  avatar_url?: string;
  timezone?: string;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  user: User;
}

// Company types
export interface Company extends BaseEntity {
  name: string;
  website?: string;
  phone?: string;
  address?: string;
  notes?: string;
  company_status_id?: string;
  is_deleted: boolean;
}

export interface CompanyStatus extends BaseEntity {
  name: string;
  pipeline_order: number;
}

export interface CompanyCreate {
  name: string;
  website?: string;
  phone?: string;
  address?: string;
  notes?: string;
  company_status_id?: string;
}

export interface CompanyUpdate {
  name?: string;
  website?: string;
  phone?: string;
  address?: string;
  notes?: string;
  company_status_id?: string;
}

// Contact types
export interface Contact extends BaseEntity {
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  job_title?: string;
  company_id?: string;
  created_by: string;
  is_deleted: boolean;
}

export interface ContactWithCompany extends Contact {
  company?: {
    id: string;
    name: string;
    is_deleted: boolean;
  };
}

export interface ContactCreate {
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  job_title?: string;
  company_id?: string;
}

export interface ContactUpdate {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  job_title?: string;
  company_id?: string;
}

// Deal types
export interface Deal extends BaseEntity {
  title: string;
  description?: string;
  estimated_value?: number;
  company_id: string;
  deal_stage_id: string;
  expected_close_date?: string;
  created_by: string;
}

export interface DealStage extends BaseEntity {
  name: string;
  pipeline_order: number;
  is_closed_won: boolean;
  is_closed_lost: boolean;
}

export interface DealCreate {
  title: string;
  description?: string;
  estimated_value?: number;
  company_id: string;
  deal_stage_id: string;
  expected_close_date?: string;
}

export interface DealUpdate {
  title?: string;
  description?: string;
  estimated_value?: number;
  company_id?: string;
  deal_stage_id?: string;
  expected_close_date?: string;
}

// API Error types
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

export interface ValidationError extends ApiError {
  details: {
    field_errors: Record<string, string[]>;
  };
}
EOF

# Create the main index file
cat > "$OUTPUT_DIR/index.ts" << 'EOF'
/**
 * CRM API Client
 * Main entry point for the CRM REST API client
 */

export { ApiClient } from './client';
export * from './types';

// Default export for convenience
export { ApiClient as default } from './client';
EOF

# Create a configuration helper
cat > "$OUTPUT_DIR/config.ts" << 'EOF'
/**
 * API Configuration utilities
 */

export interface ApiConfig {
  baseUrl: string;
  token?: string;
}

export const defaultConfig: ApiConfig = {
  baseUrl: process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1',
};

export function createApiConfig(overrides: Partial<ApiConfig> = {}): ApiConfig {
  return {
    ...defaultConfig,
    ...overrides,
  };
}
EOF

echo "Simple TypeScript API client created successfully!"
echo "Generated files:"
find "$OUTPUT_DIR" -name "*.ts" | sort