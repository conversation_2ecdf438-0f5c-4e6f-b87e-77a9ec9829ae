openapi: 3.0.3
info:
  title: CRM Contacts API
  version: 1.0.0
  description: Contact management endpoints for the CRM system

paths:
  /contacts:
    get:
      summary: List contacts
      description: Retrieve a list of all active contacts with their associated companies
      parameters:
        - name: company_id
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by company ID
        - name: unlinked
          in: query
          schema:
            type: boolean
          description: Only return contacts without a company
        - name: include_deleted
          in: query
          schema:
            type: boolean
            default: false
          description: Include soft-deleted contacts
      responses:
        '200':
          description: List of contacts
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ContactWithCompany'

    post:
      summary: Create contact
      description: Create a new contact
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactCreate'
      responses:
        '201':
          description: Contact created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contact'
        '400':
          description: Invalid input

  /contacts/{id}:
    get:
      summary: Get contact
      description: Retrieve a specific contact by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Contact details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContactDetails'
        '404':
          description: Contact not found

    put:
      summary: Update contact
      description: Update an existing contact
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactUpdate'
      responses:
        '200':
          description: Contact updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contact'
        '404':
          description: Contact not found

    delete:
      summary: Delete contact
      description: Soft delete a contact (sets is_deleted to true)
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Contact deleted successfully
        '404':
          description: Contact not found

  /contacts/{id}/company:
    put:
      summary: Link contact to company
      description: Associate a contact with a company
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                company_id:
                  type: string
                  format: uuid
              required:
                - company_id
      responses:
        '200':
          description: Contact linked to company successfully
        '404':
          description: Contact or company not found

    delete:
      summary: Unlink contact from company
      description: Remove the association between a contact and company
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Contact unlinked from company successfully
        '404':
          description: Contact not found

  /contacts/{id}/history:
    get:
      summary: Get contact history
      description: Retrieve employment history for a contact
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: List of contact history entries
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ContactHistory'

    post:
      summary: Add contact history entry
      description: Add a new employment history entry for a contact
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactHistoryCreate'
      responses:
        '201':
          description: Contact history entry created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContactHistory'

  /contacts/{id}/history/{history_id}:
    put:
      summary: Update contact history entry
      description: Update an employment history entry (typically to set end_date)
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: history_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactHistoryUpdate'
      responses:
        '200':
          description: Contact history entry updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContactHistory'
        '404':
          description: Contact or history entry not found

  /contacts/{id}/research-notes:
    get:
      summary: Get contact research notes
      description: Retrieve research notes for a contact
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: List of research notes
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ResearchNote'

components:
  schemas:
    Contact:
      type: object
      properties:
        id:
          type: string
          format: uuid
        first_name:
          type: string
        last_name:
          type: string
        email:
          type: string
          format: email
        phone:
          type: string
        job_title:
          type: string
        company_id:
          type: string
          format: uuid
          nullable: true
        created_by:
          type: string
          format: uuid
        is_deleted:
          type: boolean
          default: false
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    ContactWithCompany:
      allOf:
        - $ref: '#/components/schemas/Contact'
        - type: object
          properties:
            company:
              $ref: '#/components/schemas/CompanyInfo'

    ContactDetails:
      allOf:
        - $ref: '#/components/schemas/Contact'
        - type: object
          properties:
            company:
              $ref: '#/components/schemas/CompanyBasicInfo'
            history:
              type: array
              items:
                $ref: '#/components/schemas/ContactHistory'

    ContactCreate:
      type: object
      properties:
        first_name:
          type: string
          minLength: 1
        last_name:
          type: string
          minLength: 1
        email:
          type: string
          format: email
        phone:
          type: string
        job_title:
          type: string
        company_id:
          type: string
          format: uuid
      required:
        - first_name
        - last_name

    ContactUpdate:
      type: object
      properties:
        first_name:
          type: string
          minLength: 1
        last_name:
          type: string
          minLength: 1
        email:
          type: string
          format: email
        phone:
          type: string
        job_title:
          type: string
        company_id:
          type: string
          format: uuid
          nullable: true

    ContactHistory:
      type: object
      properties:
        id:
          type: string
          format: uuid
        contact_id:
          type: string
          format: uuid
        company_id:
          type: string
          format: uuid
        job_title:
          type: string
        start_date:
          type: string
          format: date
        end_date:
          type: string
          format: date
          nullable: true
        created_at:
          type: string
          format: date-time
        company:
          type: object
          properties:
            id:
              type: string
              format: uuid
            name:
              type: string

    ContactHistoryCreate:
      type: object
      properties:
        company_id:
          type: string
          format: uuid
        job_title:
          type: string
        start_date:
          type: string
          format: date
        end_date:
          type: string
          format: date
          nullable: true
      required:
        - company_id
        - job_title
        - start_date

    ContactHistoryUpdate:
      type: object
      properties:
        job_title:
          type: string
        start_date:
          type: string
          format: date
        end_date:
          type: string
          format: date
          nullable: true

    ResearchNote:
      type: object
      properties:
        id:
          type: string
          format: uuid
        entity_type:
          type: string
          enum: [contact, company]
        entity_id:
          type: string
          format: uuid
        note_content:
          type: string
        created_at:
          type: string
          format: date-time