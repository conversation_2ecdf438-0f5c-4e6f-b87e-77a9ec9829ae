# Contact schemas
Contact:
  type: object
  properties:
    id:
      type: string
      format: uuid
    first_name:
      type: string
    last_name:
      type: string
    email:
      type: string
      format: email
    phone:
      type: string
    job_title:
      type: string
    company_id:
      type: string
      format: uuid
      nullable: true
    created_by:
      type: string
      format: uuid
    is_deleted:
      type: boolean
      default: false
    created_at:
      type: string
      format: date-time
    updated_at:
      type: string
      format: date-time

ContactWithCompany:
  allOf:
    - $ref: '#/Contact'
    - type: object
      properties:
        company:
          $ref: '../companies/schemas.yaml#/CompanyInfo'

ContactDetails:
  allOf:
    - $ref: '#/Contact'
    - type: object
      properties:
        company:
          $ref: '../companies/schemas.yaml#/CompanyBasicInfo'

ContactCreate:
  type: object
  properties:
    first_name:
      type: string
      minLength: 1
    last_name:
      type: string
      minLength: 1
    email:
      type: string
      format: email
    phone:
      type: string
    job_title:
      type: string
    company_id:
      type: string
      format: uuid
  required:
    - first_name
    - last_name

ContactUpdate:
  type: object
  properties:
    first_name:
      type: string
      minLength: 1
    last_name:
      type: string
      minLength: 1
    email:
      type: string
      format: email
    phone:
      type: string
    job_title:
      type: string
    company_id:
      type: string
      format: uuid
      nullable: true