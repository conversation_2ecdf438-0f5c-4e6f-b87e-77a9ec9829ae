import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { prdApiClient } from '@/services/api/prd-api';
import {
  FileText,
  Sparkles,
  Brain,
  Download,
  Share2,
  Users,
  CheckCircle2,
  AlertCircle,
  Clock,
  TrendingUp
} from 'lucide-react';

// Import the new PRD components
import { RequirementsGatheringSection } from './gathering/RequirementsGatheringSection';
import { PRDDocumentSection } from './document/PRDDocumentSection';
import { AIProviderStatus } from '@/components/ai/AIProviderStatus';

interface PRDRequirementsTabProps {
  projectId: string;
}

export const PRDRequirementsTab: React.FC<PRDRequirementsTabProps> = ({ projectId }) => {
  const [activeTab, setActiveTab] = useState<'gathering' | 'prd'>('gathering');

  // Fetch real gathering sessions data
  const { data: gatheringSessions, error: sessionsError } = useQuery({
    queryKey: ['gathering-sessions', projectId],
    queryFn: () => prdApiClient.getGatheringSessions(projectId).catch(err => {
      console.warn('Failed to fetch gathering sessions:', err);
      return []; // Return empty array as fallback
    }),
    enabled: !!projectId,
    retry: false // Don't retry on error
  });

  // Fetch real PRD document data
  const { data: prdDocument, error: documentError } = useQuery({
    queryKey: ['prd-document', projectId],
    queryFn: () => prdApiClient.getPRDDocument(projectId).catch(err => {
      console.warn('Failed to fetch PRD document:', err);
      return null; // Return null as fallback
    }),
    enabled: !!projectId,
    retry: false // Don't retry on error
  });

  // Calculate real stats from API data
  const gatheringStats = {
    totalSessions: gatheringSessions?.length || 0,
    completedSessions: gatheringSessions?.filter(s => s.status === 'completed').length || 0,
    capturedItems: 0, // TODO: Calculate from captured content
    processingItems: 0, // TODO: Calculate from processing status
    extractedRequirements: 0, // TODO: Calculate from analysis results
    overallProgress: gatheringSessions?.length > 0
      ? Math.round(gatheringSessions.reduce((sum, s) => sum + s.progress_percentage, 0) / gatheringSessions.length)
      : 0
  };

  const prdStats = {
    totalSections: prdDocument?.sections?.length || 15,
    completedSections: prdDocument?.sections?.filter(s => s.status === 'complete').length || 0,
    inProgressSections: prdDocument?.sections?.filter(s => s.status === 'partial').length || 0,
    emptySections: prdDocument?.sections?.filter(s => s.status === 'empty').length || 0,
    overallCompleteness: prdDocument?.sections?.length > 0
      ? Math.round(prdDocument.sections.reduce((sum, s) => sum + s.completeness_percentage, 0) / prdDocument.sections.length)
      : 0,
    qualityScore: 0, // TODO: Get from quality analysis API
    lastUpdated: prdDocument?.updated_at ? new Date(prdDocument.updated_at).toLocaleString() : 'Never'
  };

  return (
    <div className="space-y-6">
      {/* Header with Overview Stats */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-6 w-6 text-purple-600" />
                AI-Powered Requirements & PRD System
              </CardTitle>
              <CardDescription>
                Comprehensive requirements gathering and Product Requirements Document generation
              </CardDescription>
            </div>
            <AIProviderStatus />
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{gatheringStats.capturedItems}</div>
              <p className="text-sm text-gray-600">Items Captured</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{gatheringStats.extractedRequirements}</div>
              <p className="text-sm text-gray-600">Requirements Extracted</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{prdStats.completedSections}</div>
              <p className="text-sm text-gray-600">PRD Sections Complete</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{prdStats.qualityScore}%</div>
              <p className="text-sm text-gray-600">Quality Score</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'gathering' | 'prd')}>
        <div className="flex items-center justify-between">
          <TabsList className="grid w-auto grid-cols-2">
            <TabsTrigger value="gathering" className="flex items-center gap-2 px-6 py-3">
              <Sparkles className="h-4 w-4" />
              <div className="text-left">
                <div className="font-medium">Requirements Gathering</div>
                <div className="text-xs text-gray-500">{gatheringStats.overallProgress}% Complete</div>
              </div>
            </TabsTrigger>
            <TabsTrigger value="prd" className="flex items-center gap-2 px-6 py-3">
              <FileText className="h-4 w-4" />
              <div className="text-left">
                <div className="font-medium">PRD Document</div>
                <div className="text-xs text-gray-500">{prdStats.overallCompleteness}% Complete</div>
              </div>
            </TabsTrigger>
          </TabsList>

          {/* Quick Actions */}
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Users className="h-4 w-4 mr-2" />
              Collaborate
            </Button>
            <Button variant="outline" size="sm">
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          <TabsContent value="gathering" className="space-y-6">
            {/* Gathering Progress Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-purple-600" />
                    Requirements Gathering Progress
                  </span>
                  <Badge variant="secondary">
                    {gatheringStats.completedSessions}/{gatheringStats.totalSessions} Sessions Complete
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Overall Progress</span>
                      <span className="text-sm text-gray-600">{gatheringStats.overallProgress}%</span>
                    </div>
                    <Progress value={gatheringStats.overallProgress} className="h-2" />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                      <span className="text-sm">{gatheringStats.capturedItems} items captured</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm">{gatheringStats.processingItems} items processing</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-blue-600" />
                      <span className="text-sm">{gatheringStats.extractedRequirements} requirements extracted</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Requirements Gathering Interface */}
            <RequirementsGatheringSection projectId={projectId} />
          </TabsContent>

          <TabsContent value="prd" className="space-y-6">
            {/* PRD Progress Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-blue-600" />
                    PRD Document Status
                  </span>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">
                      Quality Score: {prdStats.qualityScore}%
                    </Badge>
                    <Badge variant="outline">
                      Last updated {prdStats.lastUpdated}
                    </Badge>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Document Completeness</span>
                      <span className="text-sm text-gray-600">{prdStats.overallCompleteness}%</span>
                    </div>
                    <Progress value={prdStats.overallCompleteness} className="h-2" />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                      <span className="text-sm">{prdStats.completedSections} completed</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm">{prdStats.inProgressSections} in progress</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <span className="text-sm">{prdStats.emptySections} empty</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Brain className="h-4 w-4 text-purple-600" />
                      <span className="text-sm">AI-powered insights</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* PRD Document Interface */}
            <PRDDocumentSection projectId={projectId} />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};
