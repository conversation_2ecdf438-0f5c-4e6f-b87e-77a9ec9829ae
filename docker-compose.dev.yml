# Development override for docker-compose.yml
# This allows running services in development mode with hot reload
services:
  # Database remains the same
  postgres:
    image: postgres:17
    container_name: orbit-postgres-dev
    environment:
      POSTGRES_DB: orbit
      POSTGRES_USER: orbit_user
      POSTGRES_PASSWORD: orbit_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./infra/database/container_seed_data.sql:/docker-entrypoint-initdb.d/01_seed.sql
    networks:
      - orbit-network

  # Gateway with development configuration
  gateway:
    build:
      context: ./gateway
      dockerfile: Dockerfile
    container_name: orbit-gateway-dev
    ports:
      - "8080:8080"
    volumes:
      - ./gateway/nginx-dev.conf:/etc/nginx/conf.d/default.conf:ro
    networks:
      - orbit-network

  # For development, you can run frontend services locally with:
  # bazel run //apps/crm/web:dev (port 8081)
  # bazel run //apps/project-hub/frontend:dev (port 8082)
  # 
  # And backend services with:
  # bazel run //services/auth:auth_service (port 8004)
  # bazel run //apps/crm/backend:crm_backend (port 8005)
  # bazel run //apps/project-hub/backend:project_hub_backend (port 8003)

volumes:
  postgres_data_dev:

networks:
  orbit-network:
    driver: bridge