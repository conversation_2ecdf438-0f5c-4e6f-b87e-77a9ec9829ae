# Company schemas
Company:
  type: object
  properties:
    id:
      type: string
      format: uuid
    name:
      type: string
    website:
      type: string
      format: uri
    phone:
      type: string
    address:
      type: string
    notes:
      type: string
    company_status_id:
      type: string
      format: uuid
    is_deleted:
      type: boolean
      default: false
    created_at:
      type: string
      format: date-time
    updated_at:
      type: string
      format: date-time

CompanyDetails:
  allOf:
    - $ref: '#/Company'
    - type: object
      properties:
        contacts:
          type: array
          items:
            $ref: '../contacts/schemas.yaml#/Contact'

CompanyCreate:
  type: object
  properties:
    name:
      type: string
      minLength: 1
    website:
      type: string
      format: uri
    phone:
      type: string
    address:
      type: string
    notes:
      type: string
    company_status_id:
      type: string
      format: uuid
  required:
    - name

CompanyUpdate:
  type: object
  properties:
    name:
      type: string
      minLength: 1
    website:
      type: string
      format: uri
    phone:
      type: string
    address:
      type: string
    notes:
      type: string
    company_status_id:
      type: string
      format: uuid

CompanyStatus:
  type: object
  properties:
    id:
      type: string
      format: uuid
    name:
      type: string
    pipeline_order:
      type: integer
    created_at:
      type: string
      format: date-time

CompanyInfo:
  type: object
  properties:
    id:
      type: string
      format: uuid
    name:
      type: string
    is_deleted:
      type: boolean
  nullable: true

CompanyBasicInfo:
  type: object
  properties:
    id:
      type: string
      format: uuid
    name:
      type: string
  nullable: true