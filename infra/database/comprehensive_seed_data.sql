-- Comprehensive Seed Data for Orbit Project
-- This file combines data from orbit, appdb, and projecthub databases
-- Created from backups on 2025-07-20

-- ==================================================
-- ORBIT DATABASE DATA (CRM, Users, Companies, etc.)
-- ==================================================

-- Note: This section contains the complete orbit database schema and data
-- including users, companies, contacts, deals, and all CRM-related tables
--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13 (Homebrew)
-- Dumped by pg_dump version 15.13 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: postgres
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO orbit_user;

--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: postgres
--

COMMENT ON SCHEMA public IS '';


--
-- Name: EffortEstimate; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public."EffortEstimate" AS ENUM (
    'S',
    'M',
    'L',
    'XL'
);


ALTER TYPE public."EffortEstimate" OWNER TO orbit_user;

--
-- Name: ImpactType; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public."ImpactType" AS ENUM (
    'Revenue',
    'Platform',
    'Bug Fix',
    'R&D'
);


ALTER TYPE public."ImpactType" OWNER TO orbit_user;

--
-- Name: PriorityLevel; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public."PriorityLevel" AS ENUM (
    'P0',
    'P1',
    'P2',
    'P3',
    'P4'
);


ALTER TYPE public."PriorityLevel" OWNER TO orbit_user;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO orbit_user;

--
-- Name: deployments; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.deployments (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    project_id uuid NOT NULL,
    version text NOT NULL,
    environment text NOT NULL,
    status text DEFAULT 'pending'::text NOT NULL,
    deployed_by uuid NOT NULL,
    deployed_at timestamp(3) without time zone,
    rollback_at timestamp(3) without time zone,
    notes text,
    commit_hash text,
    build_number text,
    deployment_url text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.deployments OWNER TO orbit_user;

--
-- Name: impact_types; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.impact_types (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    label text NOT NULL,
    description text NOT NULL,
    color text NOT NULL,
    bg_color text NOT NULL,
    border_color text NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    sort_order integer DEFAULT 0 NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.impact_types OWNER TO orbit_user;

--
-- Name: permissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.permissions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    resource text NOT NULL,
    action text NOT NULL,
    description text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.permissions OWNER TO orbit_user;

--
-- Name: priority_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.priority_history (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    project_id uuid NOT NULL,
    old_priority public."PriorityLevel",
    new_priority public."PriorityLevel" NOT NULL,
    changed_by text,
    change_reason text,
    auto_escalated boolean DEFAULT false NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.priority_history OWNER TO orbit_user;

--
-- Name: priority_rules; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.priority_rules (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    from_priority public."PriorityLevel" NOT NULL,
    to_priority public."PriorityLevel" NOT NULL,
    max_days integer NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.priority_rules OWNER TO orbit_user;

--
-- Name: project_documents; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.project_documents (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    project_id uuid NOT NULL,
    title text NOT NULL,
    content text,
    document_type text DEFAULT 'general'::text NOT NULL,
    file_url text,
    file_name text,
    file_size integer,
    mime_type text,
    version text DEFAULT '1.0'::text NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_by uuid NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.project_documents OWNER TO orbit_user;

--
-- Name: project_integrations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.project_integrations (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    project_id uuid NOT NULL,
    integration_type text NOT NULL,
    integration_url text NOT NULL,
    integration_data jsonb,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.project_integrations OWNER TO orbit_user;

--
-- Name: project_milestones; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.project_milestones (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    project_id uuid NOT NULL,
    phase_id uuid,
    name text NOT NULL,
    description text,
    due_date timestamp(3) without time zone,
    completed_at timestamp(3) without time zone,
    status text DEFAULT 'pending'::text NOT NULL,
    priority text DEFAULT 'medium'::text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.project_milestones OWNER TO orbit_user;

--
-- Name: project_phases; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.project_phases (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    project_id uuid NOT NULL,
    name text NOT NULL,
    description text,
    status text DEFAULT 'not_started'::text NOT NULL,
    order_index integer NOT NULL,
    start_date timestamp(3) without time zone,
    end_date timestamp(3) without time zone,
    progress integer DEFAULT 0 NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.project_phases OWNER TO orbit_user;

--
-- Name: project_templates; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.project_templates (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    description text,
    methodology text DEFAULT 'agile'::text NOT NULL,
    category text,
    is_active boolean DEFAULT true NOT NULL,
    created_by uuid NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    default_phases jsonb,
    default_milestones jsonb,
    default_requirements jsonb,
    default_tasks jsonb
);


ALTER TABLE public.project_templates OWNER TO orbit_user;

--
-- Name: projects; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.projects (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    type text NOT NULL,
    customer_name text,
    project_lead text NOT NULL,
    customer_lead text,
    customer_contact text,
    description text,
    status text DEFAULT 'not-started'::text NOT NULL,
    company_name text DEFAULT ''::text NOT NULL,
    start_date date,
    end_date date,
    original_end_date date,
    poc_url text,
    prd_document_link text,
    priority_order integer,
    completed_at timestamp(3) without time zone,
    archived_at timestamp(3) without time zone,
    status_changed_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP,
    priority_level public."PriorityLevel" DEFAULT 'P3'::public."PriorityLevel",
    effort_estimate public."EffortEstimate" DEFAULT 'M'::public."EffortEstimate",
    priority_assigned_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP,
    last_reviewed_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP,
    auto_escalated boolean DEFAULT false,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    project_lead_id uuid,
    customer_lead_id uuid,
    impact_type_id uuid,
    budget_allocated numeric(10,2),
    budget_spent numeric(10,2),
    completion_percentage integer DEFAULT 0,
    current_phase text DEFAULT 'requirements'::text,
    methodology text DEFAULT 'agile'::text,
    project_health text DEFAULT 'green'::text,
    risk_level text DEFAULT 'low'::text,
    team_size integer DEFAULT 1,
    template_id uuid,
    impact_type text DEFAULT 'Platform'::text
);


ALTER TABLE public.projects OWNER TO orbit_user;

--
-- Name: requirements; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.requirements (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    project_id uuid NOT NULL,
    title text NOT NULL,
    description text,
    type text DEFAULT 'functional'::text NOT NULL,
    priority text DEFAULT 'medium'::text NOT NULL,
    status text DEFAULT 'draft'::text NOT NULL,
    source text,
    rationale text,
    acceptance_criteria text,
    order_index integer,
    created_by uuid NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.requirements OWNER TO orbit_user;

--
-- Name: role_permissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.role_permissions (
    role_id uuid NOT NULL,
    permission_id uuid NOT NULL
);


ALTER TABLE public.role_permissions OWNER TO orbit_user;

--
-- Name: roles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.roles (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    description text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.roles OWNER TO orbit_user;

--
-- Name: sub_tasks; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.sub_tasks (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    task_id uuid NOT NULL,
    name text NOT NULL,
    description text,
    assignee text NOT NULL,
    due_date date,
    status text DEFAULT 'to-do'::text NOT NULL,
    completed_at timestamp(3) without time zone,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    assignee_id uuid
);


ALTER TABLE public.sub_tasks OWNER TO orbit_user;

--
-- Name: tasks; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tasks (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    project_id uuid NOT NULL,
    name text NOT NULL,
    description text,
    assignee text NOT NULL,
    due_date date,
    status text DEFAULT 'to-do'::text NOT NULL,
    completed_at timestamp(3) without time zone,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    assignee_id uuid,
    actual_hours integer,
    effort_hours integer,
    milestone_id uuid,
    phase_id uuid,
    priority text DEFAULT 'medium'::text,
    progress integer DEFAULT 0,
    requirement_id uuid,
    task_type text DEFAULT 'development'::text
);


ALTER TABLE public.tasks OWNER TO orbit_user;

--
-- Name: team_members; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.team_members (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    email text NOT NULL,
    role text,
    department text,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.team_members OWNER TO orbit_user;

--
-- Name: test_cases; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.test_cases (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    project_id uuid NOT NULL,
    requirement_id uuid,
    title text NOT NULL,
    description text,
    preconditions text,
    test_steps text,
    expected_result text,
    actual_result text,
    status text DEFAULT 'not_executed'::text NOT NULL,
    priority text DEFAULT 'medium'::text NOT NULL,
    test_type text DEFAULT 'manual'::text NOT NULL,
    executed_by uuid,
    executed_at timestamp(3) without time zone,
    created_by uuid NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.test_cases OWNER TO orbit_user;

--
-- Name: user_roles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_roles (
    user_id uuid NOT NULL,
    role_id uuid NOT NULL,
    assigned_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    assigned_by text NOT NULL
);


ALTER TABLE public.user_roles OWNER TO orbit_user;

--
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    email text NOT NULL,
    name text NOT NULL,
    avatar_url text,
    google_id text,
    password_hash text,
    email_confirmed_at timestamp(3) without time zone,
    is_deleted boolean DEFAULT false NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.users OWNER TO orbit_user;

--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
f001f32f-b2eb-4ef3-8522-96c5a23f5ed7	3cba41d94c853d4f8ce8ccd342524e20896123c2719d302ceb5ed9f62816d663	2025-07-18 19:35:41.565146+05:30	20250718070521_init	\N	\N	2025-07-18 19:35:41.54448+05:30	1
929dde13-b50b-446d-9b2a-0f86eb4e8465	9d88180d828dc8fae7254a6594f2429e5cacd222d68d9ad98192fe8a4e2e8383	2025-07-18 19:35:48.072229+05:30	20250718140548_add_sdlc_workflow	\N	\N	2025-07-18 19:35:48.059949+05:30	1
a7348860-c16f-4440-a184-fd0a96b42728	267298b9c2f27d9a55ce63687bfe0be04a540fd75e7e99cc68c33fc42dad4985	2025-07-18 19:52:11.891236+05:30	20250718142211_fix_impact_type_enum	\N	\N	2025-07-18 19:52:11.888371+05:30	1
\.


--
-- Data for Name: deployments; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.deployments (id, project_id, version, environment, status, deployed_by, deployed_at, rollback_at, notes, commit_hash, build_number, deployment_url, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: impact_types; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.impact_types (id, name, label, description, color, bg_color, border_color, is_active, sort_order, created_at, updated_at) FROM stdin;
a4a2a411-867f-4aa6-a667-e4f28d8f8804	Platform	Platform	Core infrastructure, agent framework, future mission	hsl(var(--blue))	hsl(var(--blue) / 0.1)	hsl(var(--blue) / 0.3)	t	2	2025-07-17 07:46:04.383	2025-07-17 07:46:04.383
d7b98af1-031b-4aff-8c03-dd09d7b871f4	Bug Fix	Bug Fix	Resolving existing issues/technical problems	hsl(var(--purple))	hsl(var(--purple) / 0.1)	hsl(var(--purple) / 0.3)	t	3	2025-07-17 07:46:04.383	2025-07-17 07:46:04.383
516d941e-fc49-483f-ac76-79b8f944812f	R&D	R&D	Research, experimentation, proof of concepts	hsl(var(--orange))	hsl(var(--orange) / 0.1)	hsl(var(--orange) / 0.3)	t	4	2025-07-17 07:46:04.383	2025-07-17 07:46:04.383
efd7ddfd-876f-45d7-93cc-4b82e071be47	Revenu	Revenue	Direct customer/sales impact	hsl(var(--green))	hsl(var(--green) / 0.1)	hsl(var(--green) / 0.3)	t	1	2025-07-17 07:46:04.383	2025-07-17 08:05:45.748
\.


--
-- Data for Name: permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.permissions (id, name, resource, action, description, created_at, updated_at) FROM stdin;
23c28dac-c3fc-476d-a204-ef6ad52013a9	project:read	project	read	Read project information	2025-07-18 14:05:48.756	2025-07-18 14:05:53.411
87d3fe72-1610-4a07-a2c2-944af5b05d9f	project:create	project	create	Create new projects	2025-07-18 14:05:48.763	2025-07-18 14:05:53.414
b8bd2bb0-e422-49d2-9c5b-58802aff9af3	project:update	project	update	Update project information	2025-07-18 14:05:48.764	2025-07-18 14:05:53.414
a9a33175-e509-4b39-bd44-58bb29672401	project:delete	project	delete	Delete projects	2025-07-18 14:05:48.766	2025-07-18 14:05:53.415
f320c19c-50d5-42df-bc8e-2f3781c31db3	project:manage	project	manage	Full project management	2025-07-18 14:05:48.767	2025-07-18 14:05:53.416
10f728fe-f3ee-4777-93f9-1aeb20a8fc72	task:read	task	read	Read task information	2025-07-18 14:05:48.768	2025-07-18 14:05:53.416
edcf5129-ccd6-482d-b5ae-d108df0c8876	task:create	task	create	Create new tasks	2025-07-18 14:05:48.77	2025-07-18 14:05:53.417
e4383d8e-1a64-417d-b082-dac2cdc44b20	task:update	task	update	Update task information	2025-07-18 14:05:48.771	2025-07-18 14:05:53.417
d48e7630-e864-4b19-b731-1e7e808ef996	task:delete	task	delete	Delete tasks	2025-07-18 14:05:48.772	2025-07-18 14:05:53.418
6b5820b6-1b07-40af-a539-54bb097cc3a3	task:manage	task	manage	Full task management	2025-07-18 14:05:48.773	2025-07-18 14:05:53.418
7cfb93d5-d685-4780-93e9-f4f8f12ab90c	user:read	user	read	Read user information	2025-07-18 14:05:48.775	2025-07-18 14:05:53.419
1f51af93-3a42-467d-b481-94a4b03fb8b2	user:create	user	create	Create new users	2025-07-18 14:05:53.419	2025-07-18 14:05:53.419
816f3ea4-6742-4196-8294-99d0ff339cc9	user:update	user	update	Update user information	2025-07-18 14:05:48.776	2025-07-18 14:05:53.42
2f2c79e4-69e6-4b44-81ad-7d72f6b6e6f0	user:delete	user	delete	Delete users	2025-07-18 14:05:53.421	2025-07-18 14:05:53.421
167110aa-be88-44e2-ae61-e6aa3769ad76	user:manage	user	manage	Full user management	2025-07-18 14:05:48.777	2025-07-18 14:05:53.421
03d52e0b-234c-42a1-8b89-05c8f8b945c0	team_member:read	team_member	read	Read team member information	2025-07-18 14:05:48.778	2025-07-18 14:05:53.421
da876611-d25f-45a4-b7fb-a540c31a5fa1	team_member:create	team_member	create	Add team members	2025-07-18 14:05:48.779	2025-07-18 14:05:53.422
d9e65188-217a-4ef2-b7aa-dc1b83a5d989	team_member:update	team_member	update	Update team member information	2025-07-18 14:05:48.78	2025-07-18 14:05:53.422
414a98f7-d011-4e48-967b-d653e80b3729	team_member:delete	team_member	delete	Remove team members	2025-07-18 14:05:48.781	2025-07-18 14:05:53.423
755eeb01-6fa7-4134-9b99-eeb3520d6b9b	team_member:manage	team_member	manage	Full team member management	2025-07-18 14:05:53.423	2025-07-18 14:05:53.423
1834a01b-2791-4432-a034-1f95d1be5cfc	admin:manage	admin	manage	Manage admin functions	2025-07-18 14:05:48.781	2025-07-18 14:05:53.423
\.


--
-- Data for Name: priority_history; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.priority_history (id, project_id, old_priority, new_priority, changed_by, change_reason, auto_escalated, created_at) FROM stdin;
ebe73692-e71a-4538-a5ee-7fba93b83a80	90299ea5-9603-4436-b1a4-ccaf3154e391	P3	P1	\N	\N	f	2025-07-15 23:34:32.777
654b3f0b-1854-4a21-a7f9-0e61da17a172	90299ea5-9603-4436-b1a4-ccaf3154e391	P1	P2	\N	\N	f	2025-07-15 23:34:35.436
6b742f5f-67d3-41e9-af25-effb1924588f	e3ead8ad-944e-469a-88f8-02c53ece4734	P3	P1	\N	\N	f	2025-07-15 23:40:29.491
48242702-eb19-4805-a567-e43bdecf01b1	90299ea5-9603-4436-b1a4-ccaf3154e391	P2	P4	\N	\N	f	2025-07-15 23:50:54.442
bac8e519-9f60-4e01-9eab-6891c284d28e	e3ead8ad-944e-469a-88f8-02c53ece4734	P1	P2	\N	\N	f	2025-07-16 04:26:40.764
dd9e2654-7e3e-4cc9-8368-97d86f67ff1e	72b99eaf-e3d7-4d3e-910b-8f5e4f206279	P3	P0	\N	\N	f	2025-07-16 22:01:05.579
823d78a8-d31f-4522-92b3-defcdc415388	162605f2-9da4-4dcc-85e7-131018b5418a	P3	P1	\N	\N	f	2025-07-17 02:34:17.22
90346bfd-aade-4eb1-9a54-7c472d8848a6	162605f2-9da4-4dcc-85e7-131018b5418a	P1	P2	\N	\N	f	2025-07-17 02:34:19.693
09692463-fa99-4f55-a9e8-8a02e7906ea8	0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22	P3	P0	\N	\N	f	2025-07-15 22:36:16.798
9b425a2a-5538-4b32-a3ef-87fc983eb775	0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22	P0	P1	\N	\N	f	2025-07-15 22:36:23.802
007f149b-e95a-4a4e-9daf-555eb35f8957	0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22	P1	P2	\N	\N	f	2025-07-15 22:36:26.661
8b384180-bdee-4947-a107-7ae843e9f67f	0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22	P2	P3	\N	\N	f	2025-07-15 22:36:28.873
b5e99a1f-43a0-4e2d-aca2-db07b0faf75d	0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22	P3	P4	\N	\N	f	2025-07-15 22:36:31.013
20dd83d7-2fad-4b36-92ac-64c386b38edd	0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22	P4	P0	\N	\N	f	2025-07-15 22:39:09.866
f4fa2790-af12-4de5-9299-4e47bad8331b	0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22	P0	P1	\N	\N	f	2025-07-15 22:58:37.394
5dd1226c-ef8c-4734-ac89-811d5ddd929b	159d7520-8b08-4cbb-bd05-e91ea85498fd	P3	P1	\N	\N	f	2025-07-16 00:47:10.623
c719c2af-e494-4436-98bd-be0097c5d561	159d7520-8b08-4cbb-bd05-e91ea85498fd	P1	P0	\N	\N	f	2025-07-16 04:22:55.712
0ce20269-482d-405e-a903-ba098587a5a2	0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22	P1	P0	\N	\N	f	2025-07-16 04:24:27.662
80266ce2-5b23-47fe-896e-5db1b1b8ffa8	da587205-cd0f-4a27-9cba-5f4c4a658427	P3	P0	\N	\N	f	2025-07-16 04:32:57.926
\.


--
-- Data for Name: priority_rules; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.priority_rules (id, from_priority, to_priority, max_days, is_active, created_at, updated_at) FROM stdin;
27dceb17-89ff-4508-8f88-b9fe8bff8402	P2	P1	14	t	2025-07-15 20:09:47.907	2025-07-15 20:09:47.907
21a14967-a5e4-4a21-98cc-04107feb4ea6	P3	P2	28	t	2025-07-15 20:09:47.907	2025-07-15 20:09:47.907
\.


--
-- Data for Name: project_documents; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.project_documents (id, project_id, title, content, document_type, file_url, file_name, file_size, mime_type, version, is_active, created_by, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: project_integrations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.project_integrations (id, project_id, integration_type, integration_url, integration_data, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: project_milestones; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.project_milestones (id, project_id, phase_id, name, description, due_date, completed_at, status, priority, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: project_phases; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.project_phases (id, project_id, name, description, status, order_index, start_date, end_date, progress, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: project_templates; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.project_templates (id, name, description, methodology, category, is_active, created_by, created_at, updated_at, default_phases, default_milestones, default_requirements, default_tasks) FROM stdin;
\.


--
-- Data for Name: projects; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.projects (id, name, type, customer_name, project_lead, customer_lead, customer_contact, description, status, company_name, start_date, end_date, original_end_date, poc_url, prd_document_link, priority_order, completed_at, archived_at, status_changed_at, priority_level, effort_estimate, priority_assigned_at, last_reviewed_at, auto_escalated, created_at, updated_at, project_lead_id, customer_lead_id, impact_type_id, budget_allocated, budget_spent, completion_percentage, current_phase, methodology, project_health, risk_level, team_size, template_id, impact_type) FROM stdin;
e3ead8ad-944e-469a-88f8-02c53ece4734	AR Automation Agent 	external	Mark Dogan 	Kavi Koneti 	Grant Moyle	0414 343 237	The AR Automation Agent is an AI-powered system designed to streamline accounts receivable processes by automating monthly invoice outreach, capturing customer payment intentions via interactive email buttons, and escalating unresolved cases based on business rules. Integrated with Xero, Microsoft 365, and optionally Fishbowl, the agent reduces manual follow-up time by 80% and provides real-time visibility through a live dashboard. Built on Google Cloud using n8n, Supabase, and Microsoft Graph API, it supports configurable templates, escalation thresholds, and follow-up sequences, with a scalable architecture capable of handling up to 100,000 customers. A phased rollout ensures seamless integration, high adoption, and measurable improvements in AR efficiency and response tracking.	not-started	Integrated Supply Group (ISG)	2025-07-20	2025-07-31	\N		https://docs.google.com/document/d/1MKvytd5EKM0dkkm11oltJ-nnLNl9hxHyhSGyGDORocE/edit?tab=t.0	5	\N	\N	2025-07-15 07:08:57.77	P2	L	2025-07-16 04:26:40.764	2025-07-16 04:26:40.764	f	2025-07-14 03:35:33.272	2025-07-17 07:46:04.383	a9c89b12-c9cb-4727-baec-41493ccb80c7	4718b85c-5ca1-4d4a-8b42-c221466e2bd4	efd7ddfd-876f-45d7-93cc-4b82e071be47	\N	\N	0	requirements	agile	green	low	1	\N	Platform
90299ea5-9603-4436-b1a4-ccaf3154e391	Expense Creation Agent	internal		Kavi Koneti 	\N		The Accounts Email Bill Agent for Xero is designed to automate the processing of bill-related emails sent to `<EMAIL>`. It parses PDF and image attachments, extracts key invoice data, applies Australian tax and multi-currency rules, infers appropriate account codes, and drafts bills in Xero. The agent uses learning from user corrections over time and integrates with Orbit’s internal dashboard for visibility, feedback, and full auditability of each step.\n\nTargeted at Twodot’s internal accounts team, the agent supports both real-time and historical email ingestion, emphasizes accuracy over volume (<100 emails/month), and ensures compliance with audit and security standards. It leverages existing Orbit and Xero systems, with core success metrics tied to high parsing accuracy, low review rates, and significant time savings in manual processing.	not-started	TwoDot AI	2025-07-19	2025-07-30	2025-07-30		https://drive.google.com/file/d/1mxL6B7Mc8FH5K8kmzdAWL950w74erj7n/view?usp=drive_link	14	\N	\N	2025-07-15 23:34:23.982	P4	M	2025-07-15 23:50:54.442	2025-07-15 23:50:54.442	f	2025-07-15 23:34:23.982	2025-07-17 07:46:04.383	a9c89b12-c9cb-4727-baec-41493ccb80c7	\N	a4a2a411-867f-4aa6-a667-e4f28d8f8804	\N	\N	0	requirements	agile	green	low	1	\N	Platform
e0500b35-0dd9-47ec-8d03-141e394259ed	Meta Agent Platform	internal		Kavi Koneti 	\N		AI Agent platform to generate and execute AI Agents.	backlog	TwoDot AI	2025-07-13	2025-08-14	2025-08-14			1000	\N	\N	2025-07-16 09:42:06.267	P3	M	2025-07-16 09:42:06.267	2025-07-16 09:42:06.267	f	2025-07-16 09:42:06.267	2025-07-17 07:46:04.383	a9c89b12-c9cb-4727-baec-41493ccb80c7	\N	a4a2a411-867f-4aa6-a667-e4f28d8f8804	\N	\N	0	requirements	agile	green	low	1	\N	Platform
72b99eaf-e3d7-4d3e-910b-8f5e4f206279	orbit UI/UX templates and components	internal		Arjuna Kuraganti	\N		create the inital figma file to outline the look and feel of the two dot orbit	in-progress	TwoDot AI	2025-07-16	2025-07-17	2025-07-17			1000	\N	\N	2025-07-17 06:06:57.446	P0	S	2025-07-16 22:01:05.579	2025-07-16 22:01:05.579	f	2025-07-16 22:00:06.096	2025-07-17 07:46:04.383	ee2f08a6-a525-4937-9795-658cebccfed0	\N	a4a2a411-867f-4aa6-a667-e4f28d8f8804	\N	\N	0	requirements	agile	green	low	1	\N	Platform
162605f2-9da4-4dcc-85e7-131018b5418a	Candidate Assessor 	internal		Jackson Tumbridge 	\N		The Candidate Assessor is an automated AI-powered system that processes job applications sent to the company’s Careers email. When a candidate submits their CV and cover letter via email, an n8n workflow captures the incoming message, extracts the email body and CV attachment, and logs the metadata. The workflow then uses AI to determine which job role the candidate is applying for by analyzing the language in the email and cross-referencing it with open roles in a Supabase database. If a matching role is found, the system retrieves the corresponding scoring rubric and passes the candidate’s CV and email content through an AI model, which evaluates the application against key criteria such as technical fit, communication skills, and relevant experience. The model returns a score out of 100, which is stored alongside the candidate’s information and resume in the Supabase backend. The hiring team can then review candidates through a clean, user-friendly frontend built using Lovable.dev, which displays applications, scores, and rubrics in a structured and interactive interface. This end-to-end workflow streamlines candidate screening while ensuring consistent, rubric-based evaluations at scale.	in-progress	TwoDot AI	2025-07-09	2025-07-21	2025-07-21	https://preview--core-project-pulse.lovable.app/projects/new		1000	\N	\N	2025-07-17 02:29:26.577	P2	M	2025-07-17 02:34:19.693	2025-07-17 02:34:19.693	f	2025-07-17 02:29:26.577	2025-07-17 07:46:04.383	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	a4a2a411-867f-4aa6-a667-e4f28d8f8804	\N	\N	0	requirements	agile	green	low	1	\N	Platform
b04eba31-b738-40f7-9ba8-eed12d725ff2	Supply Chain Intel Agent 	external	Mark Dogan 	Kavi Koneti 	Grant Moyle		The ISG Supply Chain Intelligence Agent automates the ingestion and processing of supply chain data from emails, PDFs, and manual entries to replace a manual, reactive system with a proactive, rule-based exception monitoring platform. Built on Google Cloud with n8n, Supabase, and a React frontend, the agent identifies delays, updates dashboards in real time, and automates customer communications. Key features include exception detection, human-in-the-loop escalation, real-time container tracking, and business intelligence dashboards. Integration with Microsoft 365, SharePoint, Xero, and Fishbowl supports comprehensive data flow, with a phased rollout planned over 8 weeks to ensure reliability, accuracy, and user adoption.	backlog	Integrated Supply Group (ISG)	2025-07-13	2025-07-31	2025-07-31	\N	https://docs.google.com/document/d/1yKH90hbz7z4BgzUurmpvQMtFvLbmrkMhCTqt0cCvYMk/edit?tab=t.0	11	\N	\N	2025-07-15 07:08:57.77	P3	M	2025-07-15 20:09:47.907	2025-07-15 20:09:47.907	f	2025-07-14 03:37:32.172	2025-07-17 07:46:04.383	a9c89b12-c9cb-4727-baec-41493ccb80c7	4718b85c-5ca1-4d4a-8b42-c221466e2bd4	d7b98af1-031b-4aff-8c03-dd09d7b871f4	\N	\N	0	requirements	agile	green	low	1	\N	Bug Fix
0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22	Transcript Analyzer	external	Marcus Worrall & Leonie Rothwell	Will White 	Grant Moyle		The Meeting Transcript Analysis Agent is an AI-powered tool designed to automate the extraction of customer pain points from meeting transcripts and match them to predefined business solutions (“Problems We Solve”). Built on Google Cloud and integrated with OneDrive, Supabase, and Vertex AI, the system drastically reduces analysis time from hours to minutes while achieving over 80% accuracy. Targeted at Directors and Product Managers, the tool offers a secure React web interface with drag-and-drop upload, scoring dashboards, and rubric management. Key capabilities include NLP-driven theme analysis, multi-factor scoring, and solution recommendations, with a planned 8-week rollout across MVP, enhancement, and scaling phases.	not-started	Sprouta	2025-07-13	2025-07-30	\N	https://sprout-transcript-insights.lovable.app/	https://docs.google.com/document/d/1ZrQeuLWXw_CJinDwROl_UIJ9i_Jf_os7NpwLO4eAv10/edit?tab=t.0#heading=h.eazeg5qpsb7g	2	\N	\N	2025-07-16 04:15:33.582	P0	XL	2025-07-16 04:24:27.662	2025-07-16 04:24:27.662	f	2025-07-14 03:42:24.662	2025-07-17 07:46:04.383	94bcd00f-964e-4a50-adb6-e855be9ad3c0	4718b85c-5ca1-4d4a-8b42-c221466e2bd4	516d941e-fc49-483f-ac76-79b8f944812f	\N	\N	0	requirements	agile	green	low	1	\N	R&D
159d7520-8b08-4cbb-bd05-e91ea85498fd	PRD Service	internal		Kavi Koneti 	\N		The PRD Management Service is a centralized tool that helps teams at our AI company create, edit, and track product requirement documents in one place. Instead of using scattered tools like Google Docs or Notion, this service provides a consistent format, version control, and collaboration features. It connects directly to our project management tools, making it easy to link PRDs to tasks and track progress. This ensures everyone—from product to engineering—is aligned, speeding up development and reducing miscommunication.	backlog	TwoDot AI	2025-07-15	2025-07-20	\N			1	\N	\N	2025-07-16 02:32:03.976	P0	M	2025-07-16 04:22:55.712	2025-07-16 04:22:55.712	f	2025-07-14 07:48:35.705	2025-07-17 07:46:04.383	a9c89b12-c9cb-4727-baec-41493ccb80c7	\N	d7b98af1-031b-4aff-8c03-dd09d7b871f4	\N	\N	0	requirements	agile	green	low	1	\N	Bug Fix
da587205-cd0f-4a27-9cba-5f4c4a658427	Orbit Setup + CRM Service 	internal		Will White 	\N		Should be viewable on internal.dev.twodot.ai\n\nWe will develop a dedicated CRM Service to support customer relationship management across the platform. This service will operate as its own backend module, responsible for storing and managing client records, communication logs, pipeline stages, and task assignments. It will expose a secure API for integration with the main web application and other internal services. The CRM Service will be designed for scalability, supporting role-based access, activity tracking, and future integrations with external tools like email and calendar systems.	in-progress	TwoDot AI	2025-07-09	2025-07-16	2025-07-16			4	\N	\N	2025-07-16 10:02:58.523	P0	M	2025-07-16 04:32:57.926	2025-07-16 04:32:57.926	f	2025-07-14 07:54:57.593	2025-07-17 07:46:04.383	94bcd00f-964e-4a50-adb6-e855be9ad3c0	\N	d7b98af1-031b-4aff-8c03-dd09d7b871f4	\N	\N	0	requirements	agile	green	low	1	\N	Bug Fix
de805d7c-8848-4ec5-9778-bd80a05d4a0b	Project Dashboard 	internal		Jackson Tumbridge 	\N		A project dashboard that displays all active projects along with their current progress, attached PRDs, and linked POCs. Each project will be shown with a clear status indicator and ordered by priority to help teams focus on the most important work first. The dashboard will provide a quick overview of what’s in motion, what’s completed, and what’s still in planning, making it easy to track progress and ensure alignment across teams.	in-progress	TwoDot AI	2025-07-10	2025-07-18	\N	https://core-project-pulse.lovable.app/		7	\N	\N	2025-07-17 07:04:37.051	P3	M	2025-07-15 20:09:47.907	2025-07-15 20:09:47.907	f	2025-07-15 05:52:04.292	2025-07-17 07:46:04.383	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	d7b98af1-031b-4aff-8c03-dd09d7b871f4	\N	\N	0	requirements	agile	green	low	1	\N	Bug Fix
\.


--
-- Data for Name: requirements; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.requirements (id, project_id, title, description, type, priority, status, source, rationale, acceptance_criteria, order_index, created_by, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: role_permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.role_permissions (role_id, permission_id) FROM stdin;
133a7486-d6bb-43da-b934-7e195a413799	23c28dac-c3fc-476d-a204-ef6ad52013a9
133a7486-d6bb-43da-b934-7e195a413799	10f728fe-f3ee-4777-93f9-1aeb20a8fc72
133a7486-d6bb-43da-b934-7e195a413799	03d52e0b-234c-42a1-8b89-05c8f8b945c0
e33b155e-cecb-44d2-bd50-c27f9d77e820	23c28dac-c3fc-476d-a204-ef6ad52013a9
e33b155e-cecb-44d2-bd50-c27f9d77e820	87d3fe72-1610-4a07-a2c2-944af5b05d9f
e33b155e-cecb-44d2-bd50-c27f9d77e820	10f728fe-f3ee-4777-93f9-1aeb20a8fc72
e33b155e-cecb-44d2-bd50-c27f9d77e820	edcf5129-ccd6-482d-b5ae-d108df0c8876
e33b155e-cecb-44d2-bd50-c27f9d77e820	e4383d8e-1a64-417d-b082-dac2cdc44b20
e33b155e-cecb-44d2-bd50-c27f9d77e820	03d52e0b-234c-42a1-8b89-05c8f8b945c0
1bb06e74-b99a-4ff7-be8d-fffa4ef0b0f8	f320c19c-50d5-42df-bc8e-2f3781c31db3
1bb06e74-b99a-4ff7-be8d-fffa4ef0b0f8	6b5820b6-1b07-40af-a539-54bb097cc3a3
1bb06e74-b99a-4ff7-be8d-fffa4ef0b0f8	03d52e0b-234c-42a1-8b89-05c8f8b945c0
1bb06e74-b99a-4ff7-be8d-fffa4ef0b0f8	da876611-d25f-45a4-b7fb-a540c31a5fa1
1bb06e74-b99a-4ff7-be8d-fffa4ef0b0f8	d9e65188-217a-4ef2-b7aa-dc1b83a5d989
673492cc-30a9-44ef-8aaa-46b33899bc12	f320c19c-50d5-42df-bc8e-2f3781c31db3
673492cc-30a9-44ef-8aaa-46b33899bc12	6b5820b6-1b07-40af-a539-54bb097cc3a3
673492cc-30a9-44ef-8aaa-46b33899bc12	167110aa-be88-44e2-ae61-e6aa3769ad76
673492cc-30a9-44ef-8aaa-46b33899bc12	755eeb01-6fa7-4134-9b99-eeb3520d6b9b
42a2d3f7-2132-41d0-a3c4-91ff3c1e77fb	1834a01b-2791-4432-a034-1f95d1be5cfc
42a2d3f7-2132-41d0-a3c4-91ff3c1e77fb	f320c19c-50d5-42df-bc8e-2f3781c31db3
42a2d3f7-2132-41d0-a3c4-91ff3c1e77fb	6b5820b6-1b07-40af-a539-54bb097cc3a3
42a2d3f7-2132-41d0-a3c4-91ff3c1e77fb	167110aa-be88-44e2-ae61-e6aa3769ad76
42a2d3f7-2132-41d0-a3c4-91ff3c1e77fb	755eeb01-6fa7-4134-9b99-eeb3520d6b9b
\.


--
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.roles (id, name, description, created_at, updated_at) FROM stdin;
133a7486-d6bb-43da-b934-7e195a413799	guest	Guest user with read-only access	2025-07-18 14:05:48.782	2025-07-19 18:10:49.483
e33b155e-cecb-44d2-bd50-c27f9d77e820	user	Regular user with basic project access	2025-07-18 14:05:48.791	2025-07-19 18:10:49.488
1bb06e74-b99a-4ff7-be8d-fffa4ef0b0f8	project_manager	Project manager with full project and task management	2025-07-18 14:05:48.799	2025-07-19 18:10:49.492
673492cc-30a9-44ef-8aaa-46b33899bc12	admin	Administrator with full system access	2025-07-18 14:05:48.805	2025-07-19 18:10:49.497
42a2d3f7-2132-41d0-a3c4-91ff3c1e77fb	super_admin	Super administrator with all permissions	2025-07-18 14:05:48.808	2025-07-19 18:10:49.5
\.


--
-- Data for Name: sub_tasks; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.sub_tasks (id, task_id, name, description, assignee, due_date, status, completed_at, created_at, updated_at, assignee_id) FROM stdin;
\.


--
-- Data for Name: tasks; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.tasks (id, project_id, name, description, assignee, due_date, status, completed_at, created_at, updated_at, assignee_id, actual_hours, effort_hours, milestone_id, phase_id, priority, progress, requirement_id, task_type) FROM stdin;
49c601c3-853e-4675-86be-41d4ac86ad0e	da587205-cd0f-4a27-9cba-5f4c4a658427	Deploy Orbit + CRM in working state to GCP		Will White 	\N	in-progress	\N	2025-07-16 01:06:31.124	2025-07-16 01:06:50.714	94bcd00f-964e-4a50-adb6-e855be9ad3c0	\N	\N	\N	\N	medium	0	\N	development
ebe3de8d-5380-48db-b3a4-c41ef15c6753	da587205-cd0f-4a27-9cba-5f4c4a658427	Add Google Account Logins		Will White 	\N	to-do	\N	2025-07-16 01:07:28.545	2025-07-16 01:07:28.545	94bcd00f-964e-4a50-adb6-e855be9ad3c0	\N	\N	\N	\N	medium	0	\N	development
e129b68c-e3d4-4dc6-b999-8544babbfd6c	da587205-cd0f-4a27-9cba-5f4c4a658427	Clean up and document Deployment process		Will White 	\N	to-do	\N	2025-07-16 01:07:49.222	2025-07-16 01:07:49.222	94bcd00f-964e-4a50-adb6-e855be9ad3c0	\N	\N	\N	\N	medium	0	\N	development
41ca3fc9-0cda-417f-9ded-f641475a6997	de805d7c-8848-4ec5-9778-bd80a05d4a0b	Define Dashboard Requirements 		Jackson Tumbridge 	\N	done	\N	2025-07-16 03:21:26.175	2025-07-16 03:21:26.175	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
930d2320-f999-4068-912e-35ef72d0cc1f	de805d7c-8848-4ec5-9778-bd80a05d4a0b	Add Projects from Sheets 	Add all jobs with appropriate leads from the google sheet Kavi and Will made. 	Jackson Tumbridge 	\N	done	\N	2025-07-16 03:23:48.012	2025-07-16 03:23:48.012	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
9ec4650c-06f5-474a-a45d-c7dc4974dfa4	de805d7c-8848-4ec5-9778-bd80a05d4a0b	implementing start date, end date 	having color coded project's to see what is close to deadline and what isn't incorporated into statuses of each project.	Jackson Tumbridge 	\N	done	\N	2025-07-16 03:59:02.411	2025-07-16 03:59:05.28	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
a93cd5ce-c743-4e6f-b495-e4d45ab357ed	de805d7c-8848-4ec5-9778-bd80a05d4a0b	Talk with team	ask the team their opinion and add what they think would be good for them 	Jackson Tumbridge 	\N	done	\N	2025-07-16 04:01:35.573	2025-07-16 04:01:35.573	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
3b93a105-9f77-4457-a37e-0b236a644d58	de805d7c-8848-4ec5-9778-bd80a05d4a0b	Deploy 	So everyone can input their projects they are working on and enter their tasks. 	Jackson Tumbridge 	\N	done	\N	2025-07-16 04:02:47.93	2025-07-16 04:29:00.776	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
7fb428a6-9e80-42a7-833b-f3a9c1cfb276	0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22	Mvp internal delivery 		Will White 	2025-07-17	to-do	\N	2025-07-16 04:25:59.319	2025-07-16 04:25:59.319	94bcd00f-964e-4a50-adb6-e855be9ad3c0	\N	\N	\N	\N	medium	0	\N	development
90b9dc51-0a09-4258-8df2-57ba967190ac	de805d7c-8848-4ec5-9778-bd80a05d4a0b	Make done tasks green background and put an option to not show done tasks		Jackson Tumbridge 	2025-07-15	done	\N	2025-07-16 04:30:19.775	2025-07-16 07:52:08.224	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
53f9b90a-5e5f-445c-8bdb-5a053fbc889e	b04eba31-b738-40f7-9ba8-eed12d725ff2	Fishbowl MCP Creation 	Model Context Protocol (MCP) server for the Fishbowl Inventory system to enable AI agents to interact with inventory, order, and shipment data programmatically. The server will act as an interface layer between Fishbowl’s APIs and AI-driven agents, facilitating structured queries, automated decision-making, and workflow execution within existing infrastructure. Key capabilities include inventory lookups, order status checks, shipment tracking, and low-stock alerts, all accessible via standardized MCP endpoints. Built on Google Cloud with n8n orchestration and secured via OAuth, the server will support integrations with Supabase, Microsoft 365, and other business systems. The goal is to improve inventory visibility, reduce manual query overhead, and enable intelligent automation for supply chain operations.	Kavi Koneti 	2025-07-14	to-do	\N	2025-07-16 21:12:16.276	2025-07-16 21:12:16.276	a9c89b12-c9cb-4727-baec-41493ccb80c7	\N	\N	\N	\N	medium	0	\N	development
5f5a8665-7ec7-4a3d-a914-e704281687d8	de805d7c-8848-4ec5-9778-bd80a05d4a0b	create archive function and change toggle to show archive		Jackson Tumbridge 	2025-07-16	done	\N	2025-07-16 21:13:57.648	2025-07-17 00:25:05.389	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
85c5c296-f2b1-42fa-b0f1-e58c7ca96514	de805d7c-8848-4ec5-9778-bd80a05d4a0b	add the ability to add priority details on creation of project		Jackson Tumbridge 	2025-07-16	done	\N	2025-07-16 22:00:43.191	2025-07-17 00:31:10.457	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
3044e5a8-0780-485c-9e11-b2432c4dd7ac	de805d7c-8848-4ec5-9778-bd80a05d4a0b	add group by lead		Jackson Tumbridge 	2025-07-16	done	\N	2025-07-16 22:02:11.795	2025-07-17 00:55:01.04	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
7653456d-c8b3-4351-9ac6-c35184ee90ca	de805d7c-8848-4ec5-9778-bd80a05d4a0b	Clean up organisation and overall UI		Jackson Tumbridge 	2025-07-16	done	\N	2025-07-17 00:34:52.236	2025-07-17 00:34:52.236	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
8c0819d0-f414-403b-91df-9a5382a33629	de805d7c-8848-4ec5-9778-bd80a05d4a0b	not having the ability to covert to task or make edits to a project that in archive  		Jackson Tumbridge 	\N	done	\N	2025-07-17 01:00:46.187	2025-07-17 01:16:17.996	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
d68dc239-c019-4621-a90f-eb1d162ac526	da587205-cd0f-4a27-9cba-5f4c4a658427	Web (SPA) 	Web Application (SPA)	Kavi Koneti 	\N	done	\N	2025-07-17 01:09:04.196	2025-07-17 01:09:04.196	94bcd00f-964e-4a50-adb6-e855be9ad3c0	\N	\N	\N	\N	medium	0	\N	development
2c780c93-6d56-42f8-9e0f-ac184b4deacc	da587205-cd0f-4a27-9cba-5f4c4a658427	Gateway Service 	The Gateway Service will act as the centralized entry point for all traffic across the AI platform, handling authentication, request routing, rate limiting, and logging. It will provide a consistent interface for clients and services, improve security by enforcing unified access controls, and simplify integration with internal tools and external APIs. This service ensures scalability, observability, and reliability as the platform grows.	Kavi Koneti 	\N	done	\N	2025-07-17 01:15:51.063	2025-07-17 01:15:51.063	a9c89b12-c9cb-4727-baec-41493ccb80c7	\N	\N	\N	\N	medium	0	\N	development
bdd7677d-6d6d-41a4-89f3-8fceda414f48	da587205-cd0f-4a27-9cba-5f4c4a658427	Extended Web to feature CRM 	Extend the existing web application to include a CRM dashboard as a new feature. This dashboard will provide a centralized view of customer interactions, status updates, and communication history. It will be designed with a clean, user-friendly interface and integrate with existing user and project data. The CRM module will support filtering, search, tagging, and activity tracking, and will be built using the same tech stack as the core app to ensure seamless integration. Role-based access will control visibility, and all CRM data will be stored securely in the platform’s primary database.	Will White 	\N	done	\N	2025-07-17 01:15:59.415	2025-07-17 01:15:59.415	94bcd00f-964e-4a50-adb6-e855be9ad3c0	\N	\N	\N	\N	medium	0	\N	development
d48a892f-4736-4ba3-af47-10e3e6ad5a3a	da587205-cd0f-4a27-9cba-5f4c4a658427	Database Setup	Centralized PostgreSQL database to support our AI platform’s core services. This database will store structured data such as user activity, model configurations, and product metadata. It will include role-based access control, daily automated backups with 30-day retention, and be provisioned using standardized schema templates for consistency across services. The setup will support separate environments for development, staging, and production, with monitoring in place for performance and cost tracking. The goal is to ensure a secure, scalable, and easy-to-manage foundation for all data-driven features on the platform.	Kavi Koneti 	\N	done	\N	2025-07-17 01:16:05.839	2025-07-17 01:16:05.839	a9c89b12-c9cb-4727-baec-41493ccb80c7	\N	\N	\N	\N	medium	0	\N	development
2102b468-ed9f-47d7-9f78-285a2c811247	da587205-cd0f-4a27-9cba-5f4c4a658427	Extend Gateway For CRM API spec	Extend the existing Gateway Service to support the new CRM API, enabling secure and consistent access to CRM-related endpoints. This includes routing requests to the CRM service, enforcing authentication via API keys or JWT, and applying rate limiting per client. The CRM API spec will follow RESTful conventions and include endpoints for managing contacts, companies, interactions, and pipelines. Gateway logging and monitoring will capture usage metrics and errors for CRM traffic, ensuring visibility and reliability across the full request lifecycle.	Will White 	\N	done	\N	2025-07-17 01:16:12.978	2025-07-17 01:16:12.978	94bcd00f-964e-4a50-adb6-e855be9ad3c0	\N	\N	\N	\N	medium	0	\N	development
d938f07a-f3f5-4206-bad7-5bac6211d552	da587205-cd0f-4a27-9cba-5f4c4a658427	Extended DB for CRM 	extend the existing PostgreSQL database to support the CRM service by adding new tables for contacts, companies, interactions, and pipeline stages. These tables will be relational and linked to existing user and project data. The schema will support activity tracking, custom fields, and be managed through versioned migrations. All CRM data will be included in existing backup and monitoring processes.	Will White 	2025-07-30	done	\N	2025-07-17 01:29:30.403	2025-07-17 01:29:35.992	94bcd00f-964e-4a50-adb6-e855be9ad3c0	\N	\N	\N	\N	medium	0	\N	development
acc7c5e4-31fe-435d-9971-49692572b93f	de805d7c-8848-4ec5-9778-bd80a05d4a0b	create impact type table	change the imapct types to a table that can be changed dynamically. id, impact type, description.\n-then use the able data to populate the impact type	Jackson Tumbridge 	2025-07-16	done	\N	2025-07-17 07:04:59.4	2025-07-17 08:05:52.445	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
eb2d97ae-4287-4071-bf9a-9e8542d6b6db	de805d7c-8848-4ec5-9778-bd80a05d4a0b	retain session view	when sorting, filtering or grouping the list. if the user clicks into a project - when they go back the list filter, sort, group should survive - at the moment it clears all filters	Jackson Tumbridge 	2025-07-16	done	\N	2025-07-17 07:06:41.898	2025-07-17 08:19:49.702	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
2e0ae0f3-bcfb-45e4-a626-19b936ebe3ed	de805d7c-8848-4ec5-9778-bd80a05d4a0b	add list titles on grouping	when the list is grouped - you should still be able to see the column titles - currently they are not showing in group view	Jackson Tumbridge 	2025-07-16	to-do	\N	2025-07-17 07:10:35.042	2025-07-17 07:10:35.042	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
9b43e939-ae6a-4520-829d-8d3ba3e99fce	162605f2-9da4-4dcc-85e7-131018b5418a	Set Up Supabase Backend		Jackson Tumbridge 	\N	done	\N	2025-07-17 02:35:20.077	2025-07-17 02:35:41.123	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
15fc06f2-efee-4e88-a5b1-1943bea6d37e	162605f2-9da4-4dcc-85e7-131018b5418a	Design Frontend with Lovable		Jackson Tumbridge 	\N	done	\N	2025-07-17 02:35:39.413	2025-07-17 02:35:39.413	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
89075347-65f0-44a2-96ed-c0e5f056b117	162605f2-9da4-4dcc-85e7-131018b5418a	Create Email Ingestion Workflow in n8n		Jackson Tumbridge 	\N	done	\N	2025-07-17 02:39:30.775	2025-07-17 02:39:30.775	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
19ed9f27-2f38-47d6-8862-0700129f7c77	162605f2-9da4-4dcc-85e7-131018b5418a	Build AI Role Classifier (Prompt + Node)		Jackson Tumbridge 	\N	done	\N	2025-07-17 02:39:43.922	2025-07-17 02:39:43.922	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
70527071-188a-492e-b195-f825f8a11e35	162605f2-9da4-4dcc-85e7-131018b5418a	Implement CV Scoring via AI & Rubric		Jackson Tumbridge 	\N	done	\N	2025-07-17 02:40:02.729	2025-07-17 02:40:02.729	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
b47fcca8-2200-403e-bc4b-43db81f41d4e	162605f2-9da4-4dcc-85e7-131018b5418a	Set Up Frontend Score Display		Jackson Tumbridge 	\N	done	\N	2025-07-17 02:40:18.812	2025-07-17 02:40:18.812	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
fc622004-1396-4128-aaf1-a9b0bfe31e01	162605f2-9da4-4dcc-85e7-131018b5418a	Test with sample data 		Jackson Tumbridge 	\N	in-progress	\N	2025-07-17 02:43:05.175	2025-07-17 02:43:09.091	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
62a81c58-13de-4e65-8864-5767474ef589	162605f2-9da4-4dcc-85e7-131018b5418a	fix marking rubric sections issue 		Jackson Tumbridge 	\N	in-progress	\N	2025-07-17 02:43:51.305	2025-07-17 02:43:56.008	38e0aa56-df19-40fe-b02e-0aa7d07e76cf	\N	\N	\N	\N	medium	0	\N	development
\.


--
-- Data for Name: team_members; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.team_members (id, name, email, role, department, is_active, created_at, updated_at) FROM stdin;
4718b85c-5ca1-4d4a-8b42-c221466e2bd4	Grant Moyle	<EMAIL>	CEO	Australia	t	2025-07-12 23:02:44.807	2025-07-14 04:41:26.559
94bcd00f-964e-4a50-adb6-e855be9ad3c0	Will White 	<EMAIL>	Senior AI Engineer	Australia	t	2025-07-14 03:04:39.064	2025-07-14 04:41:38.135
a9c89b12-c9cb-4727-baec-41493ccb80c7	Kavi Koneti 	<EMAIL>	Vice President of AI	India	t	2025-07-14 03:07:33.753	2025-07-14 04:41:32.107
38e0aa56-df19-40fe-b02e-0aa7d07e76cf	Jackson Tumbridge 	<EMAIL>	Opperations Associate 	Australia	t	2025-07-14 03:10:02.598	2025-07-14 04:41:20.976
ee2f08a6-a525-4937-9795-658cebccfed0	Arjuna Kuraganti	<EMAIL>	Developer	India	t	2025-07-16 21:58:46.011	2025-07-16 21:58:46.011
\.


--
-- Data for Name: test_cases; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.test_cases (id, project_id, requirement_id, title, description, preconditions, test_steps, expected_result, actual_result, status, priority, test_type, executed_by, executed_at, created_by, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: user_roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_roles (user_id, role_id, assigned_at, assigned_by) FROM stdin;
b54a12e9-1d31-4ce6-8e86-f610c2e399c0	1bb06e74-b99a-4ff7-be8d-fffa4ef0b0f8	2025-07-18 14:24:46.932	b54a12e9-1d31-4ce6-8e86-f610c2e399c0
b54a12e9-1d31-4ce6-8e86-f610c2e399c0	42a2d3f7-2132-41d0-a3c4-91ff3c1e77fb	2025-07-18 14:24:46.934	b54a12e9-1d31-4ce6-8e86-f610c2e399c0
b54a12e9-1d31-4ce6-8e86-f610c2e399c0	e33b155e-cecb-44d2-bd50-c27f9d77e820	2025-07-18 14:24:46.935	b54a12e9-1d31-4ce6-8e86-f610c2e399c0
b54a12e9-1d31-4ce6-8e86-f610c2e399c0	673492cc-30a9-44ef-8aaa-46b33899bc12	2025-07-18 14:24:46.937	b54a12e9-1d31-4ce6-8e86-f610c2e399c0
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, email, name, avatar_url, google_id, is_active, created_at, updated_at, password_hash) FROM stdin;
b54a12e9-1d31-4ce6-8e86-f610c2e399c0	<EMAIL>	Kavi Koneti	https://lh3.googleusercontent.com/a/ACg8ocKxA8sQ_OUZaE5hNiujjZ3EtV6NnYv7n4bVNhC32GbuLbtYUw=s96-c	115629479485622152548	t	2025-07-18 14:13:46.918	2025-07-19 00:44:35.619	$2a$10$Y0UIvRKEXzV3zwIAa3fE0eWBIFEkDom9lawO31p3JXy5C4pVRndJu
\.


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: deployments deployments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.deployments
    ADD CONSTRAINT deployments_pkey PRIMARY KEY (id);


--
-- Name: impact_types impact_types_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.impact_types
    ADD CONSTRAINT impact_types_pkey PRIMARY KEY (id);


--
-- Name: permissions permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT permissions_pkey PRIMARY KEY (id);


--
-- Name: priority_history priority_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.priority_history
    ADD CONSTRAINT priority_history_pkey PRIMARY KEY (id);


--
-- Name: priority_rules priority_rules_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.priority_rules
    ADD CONSTRAINT priority_rules_pkey PRIMARY KEY (id);


--
-- Name: project_documents project_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_documents
    ADD CONSTRAINT project_documents_pkey PRIMARY KEY (id);


--
-- Name: project_integrations project_integrations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_integrations
    ADD CONSTRAINT project_integrations_pkey PRIMARY KEY (id);


--
-- Name: project_milestones project_milestones_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_milestones
    ADD CONSTRAINT project_milestones_pkey PRIMARY KEY (id);


--
-- Name: project_phases project_phases_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_phases
    ADD CONSTRAINT project_phases_pkey PRIMARY KEY (id);


--
-- Name: project_templates project_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_templates
    ADD CONSTRAINT project_templates_pkey PRIMARY KEY (id);


--
-- Name: projects projects_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_pkey PRIMARY KEY (id);


--
-- Name: requirements requirements_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.requirements
    ADD CONSTRAINT requirements_pkey PRIMARY KEY (id);


--
-- Name: role_permissions role_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_pkey PRIMARY KEY (role_id, permission_id);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (id);


--
-- Name: sub_tasks sub_tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sub_tasks
    ADD CONSTRAINT sub_tasks_pkey PRIMARY KEY (id);


--
-- Name: tasks tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_pkey PRIMARY KEY (id);


--
-- Name: team_members team_members_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.team_members
    ADD CONSTRAINT team_members_pkey PRIMARY KEY (id);


--
-- Name: test_cases test_cases_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.test_cases
    ADD CONSTRAINT test_cases_pkey PRIMARY KEY (id);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (user_id, role_id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: impact_types_name_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX impact_types_name_key ON public.impact_types USING btree (name);


--
-- Name: permissions_name_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX permissions_name_key ON public.permissions USING btree (name);


--
-- Name: roles_name_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX roles_name_key ON public.roles USING btree (name);


--
-- Name: team_members_email_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX team_members_email_key ON public.team_members USING btree (email);


--
-- Name: users_email_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX users_email_key ON public.users USING btree (email);


--
-- Name: users_google_id_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX users_google_id_key ON public.users USING btree (google_id);


--
-- Name: deployments deployments_deployed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.deployments
    ADD CONSTRAINT deployments_deployed_by_fkey FOREIGN KEY (deployed_by) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: deployments deployments_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.deployments
    ADD CONSTRAINT deployments_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: priority_history priority_history_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.priority_history
    ADD CONSTRAINT priority_history_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: project_documents project_documents_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_documents
    ADD CONSTRAINT project_documents_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: project_documents project_documents_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_documents
    ADD CONSTRAINT project_documents_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: project_integrations project_integrations_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_integrations
    ADD CONSTRAINT project_integrations_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: project_milestones project_milestones_phase_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_milestones
    ADD CONSTRAINT project_milestones_phase_id_fkey FOREIGN KEY (phase_id) REFERENCES public.project_phases(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: project_milestones project_milestones_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_milestones
    ADD CONSTRAINT project_milestones_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: project_phases project_phases_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_phases
    ADD CONSTRAINT project_phases_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: project_templates project_templates_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_templates
    ADD CONSTRAINT project_templates_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: projects projects_customer_lead_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_customer_lead_id_fkey FOREIGN KEY (customer_lead_id) REFERENCES public.team_members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: projects projects_impact_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_impact_type_id_fkey FOREIGN KEY (impact_type_id) REFERENCES public.impact_types(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: projects projects_project_lead_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_project_lead_id_fkey FOREIGN KEY (project_lead_id) REFERENCES public.team_members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: projects projects_template_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_template_id_fkey FOREIGN KEY (template_id) REFERENCES public.project_templates(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: requirements requirements_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.requirements
    ADD CONSTRAINT requirements_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: requirements requirements_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.requirements
    ADD CONSTRAINT requirements_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: role_permissions role_permissions_permission_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES public.permissions(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: role_permissions role_permissions_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: sub_tasks sub_tasks_assignee_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sub_tasks
    ADD CONSTRAINT sub_tasks_assignee_id_fkey FOREIGN KEY (assignee_id) REFERENCES public.team_members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: sub_tasks sub_tasks_task_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sub_tasks
    ADD CONSTRAINT sub_tasks_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.tasks(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: tasks tasks_assignee_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_assignee_id_fkey FOREIGN KEY (assignee_id) REFERENCES public.team_members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: tasks tasks_milestone_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_milestone_id_fkey FOREIGN KEY (milestone_id) REFERENCES public.project_milestones(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: tasks tasks_phase_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_phase_id_fkey FOREIGN KEY (phase_id) REFERENCES public.project_phases(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: tasks tasks_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: tasks tasks_requirement_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_requirement_id_fkey FOREIGN KEY (requirement_id) REFERENCES public.requirements(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: test_cases test_cases_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.test_cases
    ADD CONSTRAINT test_cases_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: test_cases test_cases_executed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.test_cases
    ADD CONSTRAINT test_cases_executed_by_fkey FOREIGN KEY (executed_by) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: test_cases test_cases_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.test_cases
    ADD CONSTRAINT test_cases_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: test_cases test_cases_requirement_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.test_cases
    ADD CONSTRAINT test_cases_requirement_id_fkey FOREIGN KEY (requirement_id) REFERENCES public.requirements(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: user_roles user_roles_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_roles user_roles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: postgres
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;


--
-- PostgreSQL database dump complete
--

