{"permissions": {"allow": ["Bash(find:*)", "Bash(bazel build:*)", "Bash(grep:*)", "Bash(./generate.sh:*)", "Bash(lsof:*)", "<PERSON><PERSON>(curl:*)", "Bash(GATEWAY_PORT=8080 go run main.go)", "<PERSON><PERSON>(bazel run:*)", "Bash(bazel query:*)", "Bash(npm run dev:*)", "Bash(npm install:*)", "Bash(rm:*)", "<PERSON><PERSON>(go run:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(npm run build:*)", "Bash(brew services:*)", "Bash(ls:*)", "Bash(npm run start:dev:*)", "Bash(node:*)", "Bash(kill:*)", "<PERSON><PERSON>(open:*)", "WebFetch(domain:localhost)", "Bash(npm start)", "Bash(npm run start:gateway:*)", "Bash(npm run:*)", "<PERSON><PERSON>(diff:*)", "Bash(timeout 10 bazel run //gateway:gateway)", "<PERSON><PERSON>(gtimeout:*)", "Bash(psql:*)", "<PERSON><PERSON>(pkill:*)", "Bash(brew install:*)", "Bash(nginx:*)", "<PERSON><PERSON>(sudo:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(true)", "Bash(cp:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(docker logs:*)", "Bash(docker system prune:*)", "Bash(./scripts/dev-setup.sh:*)", "Bash(./scripts/dev-status.sh:*)", "Bash(./scripts/dev-stop.sh:*)", "Bash(PGPASSWORD=orbit_password psql -h localhost -p 5433 -U orbit_user -d orbit -c \"SELECT ''Users:'' as type, count(*) as count FROM users UNION ALL SELECT ''Companies:'', count(*) FROM companies UNION ALL SELECT ''Contacts:'', count(*) FROM contacts UNION ALL SELECT ''Deals:'', count(*) FROM deals;\")", "Bash(docker volume prune:*)", "Bash(PGPASSWORD=orbit_password psql -h localhost -p 5433 -U orbit_user -d orbit -c \"SELECT ''Users:'' as type, count(*) as count FROM users UNION ALL SELECT ''Companies:'', count(*) FROM companies UNION ALL SELECT ''Contacts:'', count(*) FROM contacts UNION ALL SELECT ''Deals:'', count(*) FROM deals;\")", "Bash(PGPASSWORD=orbit_password psql:*)", "<PERSON><PERSON>(docker rm:*)", "<PERSON><PERSON>(bazel:*)", "Bash(DATABASE_URL=\"postgres://orbit_user:orbit_password@localhost:5432/orbit?sslmode=disable\" npx prisma migrate deploy)", "Bash(DATABASE_URL:*)", "Bash(go mod:*)", "Bash(go get:*)", "Bash(./scripts/zero2hero.sh:*)", "<PERSON><PERSON>(docker:*)", "Bash(/dev/null)", "Bash(echo)", "Bash(npx prisma migrate:*)", "<PERSON><PERSON>(timeout:*)", "Bash(echo \"🚀 TESTING BAZEL ZERO2HERO TARGET (FIXED)\" echo \"==========================================\" echo \"\" echo \"Testing: bazel run //:zero2hero\" echo \"\" bazel run //:zero2hero)", "Bash(for envfile in \"./apps/crm/web/.env.production\" \"./apps/crm/web/.env.local\" \"./apps/project-hub/frontend/.env\" \"./apps/project-hub/backend/.env\")", "Bash(do)", "Bash(if [ -f \"$envfile\" ])", "<PERSON><PERSON>(then)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(head:*)", "Bash(fi)", "Bash(done)", "Bash(ssh:*)"], "deny": []}}