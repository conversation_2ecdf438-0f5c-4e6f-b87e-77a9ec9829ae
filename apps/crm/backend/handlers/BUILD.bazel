load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "handlers",
    srcs = [
        "companies.go",
        "companies_impl.go",
        "contacts.go",
        "contacts_impl.go",
        "deals.go",
        "deals_impl.go",
        "helpers.go",
        "users.go",
        "users_impl.go",
    ],
    importpath = "github.com/TwoDotAi/orbit/apps/crm/backend/handlers",
    visibility = ["//visibility:public"],
    deps = [
        "//api/openapi:rest-api",
        "//apps/crm/backend/database",
        "//libs/go/logging",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@org_uber_go_zap//:zap",
    ],
)
