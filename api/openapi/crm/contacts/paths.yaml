# Contact paths with named anchors
contacts: &contacts
  get:
    tags: [Contacts]
    summary: List contacts
    description: Retrieve a list of all active contacts with their associated companies
    parameters:
      - name: company_id
        in: query
        schema:
          type: string
          format: uuid
        description: Filter by company ID
      - name: unlinked
        in: query
        schema:
          type: boolean
        description: Only return contacts without a company
      - name: include_deleted
        in: query
        schema:
          type: boolean
          default: false
        description: Include soft-deleted contacts
    responses:
      '200':
        description: List of contacts
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: './schemas.yaml#/ContactWithCompany'
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'

  post:
    tags: [Contacts]
    summary: Create contact
    description: Create a new contact
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/ContactCreate'
    responses:
      '201':
        description: Contact created successfully
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/Contact'
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'

contacts-by-id: &contacts-by-id
  get:
    tags: [Contacts]
    summary: Get contact
    description: Retrieve a specific contact by ID
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '200':
        description: Contact details
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/ContactDetails'
      '404':
        description: Contact not found
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'

  put:
    tags: [Contacts]
    summary: Update contact
    description: Update an existing contact
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/ContactUpdate'
    responses:
      '200':
        description: Contact updated successfully
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/Contact'
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'
      '404':
        description: Contact not found
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'

  delete:
    tags: [Contacts]
    summary: Delete contact
    description: Soft delete a contact (sets is_deleted to true)
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '204':
        description: Contact deleted successfully
      '404':
        description: Contact not found
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'

contacts-company: &contacts-company
  put:
    tags: [Contacts]
    summary: Link contact to company
    description: Associate a contact with a company
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            title: ContactCompanyUpdateRequest
            properties:
              company_id:
                type: string
                format: uuid
            required:
              - company_id
    responses:
      '200':
        description: Contact linked to company successfully
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'
      '404':
        description: Contact or company not found
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'

  delete:
    tags: [Contacts]
    summary: Unlink contact from company
    description: Remove the association between a contact and company
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '204':
        description: Contact unlinked from company successfully
      '404':
        description: Contact not found
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'