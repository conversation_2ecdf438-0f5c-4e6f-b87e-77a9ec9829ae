import fs from 'fs';
import path from 'path';
import { PrismaClient } from '../../generated/prisma/index.js';
import { aiProviderService, AIMessage } from './ai/aiProviderService.js';

const prisma = new PrismaClient();

export interface DocumentAnalysisResult {
  requirements: Array<{
    title: string;
    description: string;
    type: 'functional' | 'non-functional' | 'constraint' | 'assumption';
    priority: 'low' | 'medium' | 'high' | 'critical';
    acceptance_criteria?: string;
    source: string;
  }>;
  metadata: {
    documentType: string;
    pageCount?: number;
    wordCount?: number;
    language: string;
    extractedAt: string;
  };
  confidence: number;
}

export interface ConversationAnalysisResult {
  requirements: Array<{
    title: string;
    description: string;
    type: 'functional' | 'non-functional' | 'constraint' | 'assumption';
    priority: 'low' | 'medium' | 'high' | 'critical';
    acceptance_criteria?: string;
    rationale?: string;
  }>;
  suggestions: string[];
  confidence: number;
}

export class AIAnalysisService {
  private static instance: AIAnalysisService;

  public static getInstance(): AIAnalysisService {
    if (!AIAnalysisService.instance) {
      AIAnalysisService.instance = new AIAnalysisService();
    }
    return AIAnalysisService.instance;
  }

  /**
   * Extract JSON from AI response that might be wrapped in markdown code blocks
   */
  private extractJsonFromResponse(content: string): any {
    try {
      // First try direct JSON parsing
      return JSON.parse(content);
    } catch (error) {
      // If that fails, try to extract JSON from markdown code blocks
      const jsonMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[1]);
      }
      
      // Try to find JSON-like content without code blocks
      const jsonPattern = /\{[\s\S]*\}/;
      const match = content.match(jsonPattern);
      if (match) {
        return JSON.parse(match[0]);
      }
      
      throw new Error(`Could not extract valid JSON from response: ${content.substring(0, 200)}...`);
    }
  }

  /**
   * Analyze uploaded document and extract requirements using real AI
   */
  async analyzeDocument(
    filePath: string,
    fileName: string,
    mimeType: string
  ): Promise<DocumentAnalysisResult> {
    try {
      console.log(`🤖 Analyzing document: ${fileName} with multi-AI providers...`);

      const fileContent = await this.extractTextFromFile(filePath, mimeType);

      // Check if any AI providers are available
      const availableProviders = aiProviderService.getAvailableProviders();
      if (availableProviders.length === 0) {
        throw new Error('No AI providers configured. Please set up at least one AI provider (OpenAI, Gemini, Claude, Kimi, or OpenRouter).');
      }

      return await this.performRealAIDocumentAnalysis(fileContent, fileName);
    } catch (error) {
      console.error('Document analysis error:', error);
      throw new Error(`Failed to analyze document: ${error.message}`);
    }
  }

  /**
   * Analyze conversational input and generate requirements using real AI
   */
  async analyzeConversation(input: string): Promise<ConversationAnalysisResult> {
    try {
      console.log(`🤖 Analyzing conversation with multi-AI providers...`);

      // Check if any AI providers are available
      const availableProviders = aiProviderService.getAvailableProviders();
      if (availableProviders.length === 0) {
        throw new Error('No AI providers configured. Please set up at least one AI provider.');
      }

      return await this.performRealAIConversationAnalysis(input);
    } catch (error) {
      console.error('Conversation analysis error:', error);
      throw new Error(`Failed to analyze conversation: ${error.message}`);
    }
  }

  /**
   * Generate smart templates based on project type using real AI
   */
  async generateTemplate(projectType: string, industry?: string): Promise<DocumentAnalysisResult> {
    try {
      console.log(`🤖 Generating template for ${projectType} with multi-AI providers...`);

      // Check if any AI providers are available
      const availableProviders = aiProviderService.getAvailableProviders();
      if (availableProviders.length === 0) {
        throw new Error('No AI providers configured. Please set up at least one AI provider.');
      }

      return await this.performRealAITemplateGeneration(projectType, industry);
    } catch (error) {
      console.error('Template generation error:', error);
      throw new Error(`Failed to generate template: ${error.message}`);
    }
  }

  /**
   * Real AI template generation using OpenAI
   */
  private async performRealAITemplateGeneration(projectType: string, industry?: string): Promise<DocumentAnalysisResult> {
    try {
      const industryContext = industry ? `for the ${industry} industry` : '';
      const systemPrompt = 'You are an expert software architect specializing in requirements generation. Always return valid JSON.';

      const userPrompt = `
Generate comprehensive requirements for a ${projectType} project ${industryContext}.

Project Type: ${projectType}
Industry: ${industry || 'General'}

Please generate requirements in the following JSON format:
{
  "requirements": [
    {
      "title": "Requirement title",
      "description": "Detailed description",
      "type": "functional|non-functional|constraint|assumption",
      "priority": "low|medium|high|critical",
      "acceptance_criteria": "How to verify this requirement",
      "source": "AI Template: ${projectType}"
    }
  ],
  "metadata": {
    "documentType": "AI-Generated Template",
    "language": "English",
    "extractedAt": "${new Date().toISOString()}"
  },
  "confidence": 0.95
}

Generate 8-15 comprehensive requirements covering:
1. Core functionality
2. User experience
3. Security requirements
4. Performance requirements
5. Integration needs
6. Data management
7. Compliance (if industry-specific)
8. Scalability and maintenance

Make requirements specific to ${projectType} and ${industry || 'general business'} context.
Return only valid JSON, no additional text.`;

      const messages: AIMessage[] = [
        { role: 'user', content: userPrompt }
      ];

      const response = await aiProviderService.generateResponse(messages, {
        systemPrompt,
        temperature: 0.4,
        maxTokens: 2500
      });

      const templateResult = JSON.parse(response.content);

      console.log(`✅ ${response.provider} generated ${templateResult.requirements?.length || 0} template requirements`);

      return {
        requirements: templateResult.requirements || [],
        metadata: {
          documentType: "AI-Generated Template",
          language: "English",
          extractedAt: new Date().toISOString()
        },
        confidence: templateResult.confidence || 0.95
      };
    } catch (error) {
      console.error('Real AI template generation failed:', error);
      throw new Error(`AI template generation failed: ${error.message}`);
    }
  }

  /**
   * Mock template generation (fallback when no AI key)
   */
  private async performMockTemplateGeneration(projectType: string, industry?: string): Promise<DocumentAnalysisResult> {
    const templates = {
      'web-application': {
        requirements: [
          {
            title: "User Authentication System",
            description: "Users must be able to register, login, and manage their accounts securely",
            type: "functional" as const,
            priority: "high" as const,
            acceptance_criteria: "Users can register with email, login with credentials, reset password, and update profile information",
            source: "AI Template: Web Application"
          },
          {
            title: "Responsive Design Requirements",
            description: "The application must be fully responsive and work on all device sizes",
            type: "functional" as const,
            priority: "high" as const,
            acceptance_criteria: "UI adapts seamlessly to screen sizes from 320px to 1920px width",
            source: "AI Template: Web Application"
          },
          {
            title: "Performance Requirements",
            description: "The application must load quickly and respond to user interactions promptly",
            type: "non-functional" as const,
            priority: "medium" as const,
            acceptance_criteria: "Initial page load under 3 seconds, user interactions respond within 200ms",
            source: "AI Template: Web Application"
          },
          {
            title: "Security Requirements",
            description: "All user data must be protected and transmitted securely",
            type: "non-functional" as const,
            priority: "critical" as const,
            acceptance_criteria: "HTTPS encryption, secure session management, input validation, XSS protection",
            source: "AI Template: Web Application"
          }
        ],
        metadata: {
          documentType: "AI-Generated Template",
          language: "English",
          extractedAt: new Date().toISOString()
        },
        confidence: 0.95
      },
      'api-service': {
        requirements: [
          {
            title: "RESTful API Design",
            description: "API must follow REST principles with proper HTTP methods and status codes",
            type: "functional" as const,
            priority: "high" as const,
            acceptance_criteria: "Proper use of GET, POST, PUT, DELETE methods with appropriate status codes",
            source: "AI Template: API Service"
          },
          {
            title: "API Authentication",
            description: "Secure authentication mechanism for API access",
            type: "functional" as const,
            priority: "critical" as const,
            acceptance_criteria: "JWT or OAuth2 implementation with proper token validation",
            source: "AI Template: API Service"
          },
          {
            title: "Rate Limiting",
            description: "API must implement rate limiting to prevent abuse",
            type: "non-functional" as const,
            priority: "medium" as const,
            acceptance_criteria: "Configurable rate limits per user/IP with proper error responses",
            source: "AI Template: API Service"
          },
          {
            title: "API Documentation",
            description: "Comprehensive API documentation must be available",
            type: "constraint" as const,
            priority: "medium" as const,
            acceptance_criteria: "OpenAPI/Swagger documentation with examples and testing interface",
            source: "AI Template: API Service"
          }
        ],
        metadata: {
          documentType: "AI-Generated Template",
          language: "English",
          extractedAt: new Date().toISOString()
        },
        confidence: 0.95
      }
    };

    return templates[projectType as keyof typeof templates] || templates['web-application'];
  }

  private async extractTextFromFile(filePath: string, mimeType: string): Promise<string> {
    // In production, this would use libraries like:
    // - pdf-parse for PDFs
    // - mammoth for Word documents
    // - xlsx for Excel files
    // - Tesseract.js for image OCR
    
    // For now, simulate text extraction
    const fileName = path.basename(filePath).toLowerCase();
    
    if (fileName.includes('requirement') || fileName.includes('spec')) {
      return `
        User Authentication Requirements:
        - Users must be able to register with email and password
        - System should support password reset functionality
        - Multi-factor authentication should be available
        
        Performance Requirements:
        - System must respond within 200ms for 95% of requests
        - Database queries should be optimized
        - Caching should be implemented for frequently accessed data
        
        Security Requirements:
        - All data must be encrypted in transit and at rest
        - Input validation must be implemented
        - Session management must be secure
      `;
    }
    
    return `Sample document content for analysis from ${fileName}`;
  }

  private async performDocumentAnalysis(content: string, fileName: string): Promise<DocumentAnalysisResult> {
    // Simulate intelligent document analysis
    const requirements = [];
    
    // Look for authentication-related content
    if (content.toLowerCase().includes('authentication') || content.toLowerCase().includes('login')) {
      requirements.push({
        title: "User Authentication System",
        description: "System must provide secure user authentication capabilities",
        type: "functional" as const,
        priority: "high" as const,
        acceptance_criteria: "Users can register, login, and manage their accounts securely",
        source: `AI Analysis: ${fileName}`
      });
    }
    
    // Look for performance-related content
    if (content.toLowerCase().includes('performance') || content.toLowerCase().includes('response time')) {
      requirements.push({
        title: "System Performance Requirements",
        description: "System must meet specified performance criteria",
        type: "non-functional" as const,
        priority: "medium" as const,
        acceptance_criteria: "System responds within specified time limits under normal load",
        source: `AI Analysis: ${fileName}`
      });
    }
    
    // Look for security-related content
    if (content.toLowerCase().includes('security') || content.toLowerCase().includes('encryption')) {
      requirements.push({
        title: "Security Requirements",
        description: "System must implement comprehensive security measures",
        type: "non-functional" as const,
        priority: "critical" as const,
        acceptance_criteria: "Data encryption, secure transmission, and access controls implemented",
        source: `AI Analysis: ${fileName}`
      });
    }
    
    // Default requirement if no specific patterns found
    if (requirements.length === 0) {
      requirements.push({
        title: "General System Requirement",
        description: `Requirement extracted from ${fileName}`,
        type: "functional" as const,
        priority: "medium" as const,
        acceptance_criteria: "System meets the specified functional requirements",
        source: `AI Analysis: ${fileName}`
      });
    }
    
    return {
      requirements,
      metadata: {
        documentType: this.detectDocumentType(fileName),
        wordCount: content.split(' ').length,
        language: "English",
        extractedAt: new Date().toISOString()
      },
      confidence: 0.8 + Math.random() * 0.15
    };
  }

  private async extractRequirementsFromText(input: string): Promise<ConversationAnalysisResult['requirements']> {
    const requirements = [];
    const lowerInput = input.toLowerCase();
    
    // Analyze input for different types of requirements
    if (lowerInput.includes('user') && (lowerInput.includes('register') || lowerInput.includes('login') || lowerInput.includes('account'))) {
      requirements.push({
        title: "User Management System",
        description: "Users should be able to create and manage their accounts",
        type: "functional" as const,
        priority: "high" as const,
        acceptance_criteria: "Users can register, login, and update their profiles",
        rationale: "Essential for user identification and personalization"
      });
    }
    
    if (lowerInput.includes('secure') || lowerInput.includes('security') || lowerInput.includes('protect')) {
      requirements.push({
        title: "Security Requirements",
        description: "System must implement comprehensive security measures",
        type: "non-functional" as const,
        priority: "critical" as const,
        acceptance_criteria: "Data protection, secure authentication, and access controls",
        rationale: "Critical for protecting user data and system integrity"
      });
    }
    
    if (lowerInput.includes('fast') || lowerInput.includes('performance') || lowerInput.includes('speed')) {
      requirements.push({
        title: "Performance Requirements",
        description: "System must provide fast and responsive user experience",
        type: "non-functional" as const,
        priority: "medium" as const,
        acceptance_criteria: "Quick response times and efficient resource usage",
        rationale: "Important for user satisfaction and system scalability"
      });
    }
    
    // Default requirement based on input
    if (requirements.length === 0) {
      requirements.push({
        title: "Core System Functionality",
        description: "System must provide the core functionality as described",
        type: "functional" as const,
        priority: "medium" as const,
        acceptance_criteria: "System meets the basic functional requirements",
        rationale: "Based on user input analysis"
      });
    }
    
    return requirements;
  }

  private async generateSuggestions(input: string, requirements: any[]): Promise<string[]> {
    const suggestions = [
      "Consider adding more specific acceptance criteria",
      "Think about edge cases and error handling",
      "Define measurable success metrics",
      "Consider user experience implications",
      "Plan for scalability and future growth"
    ];
    
    // Add context-specific suggestions
    if (input.toLowerCase().includes('mobile')) {
      suggestions.push("Consider mobile-specific requirements and constraints");
    }
    
    if (input.toLowerCase().includes('api')) {
      suggestions.push("Define API versioning and backward compatibility requirements");
    }
    
    return suggestions.slice(0, 3); // Return top 3 suggestions
  }

  private detectDocumentType(fileName: string): string {
    const name = fileName.toLowerCase();

    if (name.includes('requirement') || name.includes('req')) return "Requirements Document";
    if (name.includes('spec') || name.includes('specification')) return "Technical Specification";
    if (name.includes('design')) return "Design Document";
    if (name.includes('user') && name.includes('story')) return "User Stories";
    if (name.includes('test')) return "Test Documentation";

    return "General Document";
  }

  /**
   * Real AI document analysis using OpenAI
   */
  private async performRealAIDocumentAnalysis(content: string, fileName: string): Promise<DocumentAnalysisResult> {
    try {
      const systemPrompt = 'You are an expert business analyst specializing in requirements extraction. Always return valid JSON.';

      const userPrompt = `
Analyze the following document content and extract structured requirements.

Document: ${fileName}
Content: ${content}

Please extract requirements in the following JSON format:
{
  "requirements": [
    {
      "title": "Brief requirement title",
      "description": "Detailed description of what needs to be implemented",
      "type": "functional|non-functional|constraint|assumption",
      "priority": "low|medium|high|critical",
      "acceptance_criteria": "How to verify this requirement is met",
      "source": "Document source or stakeholder"
    }
  ],
  "metadata": {
    "documentType": "Type of document",
    "language": "Document language",
    "extractedAt": "${new Date().toISOString()}"
  },
  "confidence": 0.95
}

Focus on:
1. Clear, actionable requirements
2. Proper categorization (functional vs non-functional)
3. Realistic priority levels
4. Testable acceptance criteria
5. Business value and rationale

Return only valid JSON, no additional text.`;

      const messages: AIMessage[] = [
        { role: 'user', content: userPrompt }
      ];

      const response = await aiProviderService.generateResponse(messages, {
        systemPrompt,
        temperature: 0.3,
        maxTokens: 2000
      });

      // Parse AI response
      const analysisResult = this.extractJsonFromResponse(response.content);

      console.log(`✅ ${response.provider} extracted ${analysisResult.requirements?.length || 0} requirements`);

      return {
        requirements: analysisResult.requirements || [],
        metadata: {
          documentType: analysisResult.metadata?.documentType || this.detectDocumentType(fileName),
          language: analysisResult.metadata?.language || "English",
          extractedAt: new Date().toISOString()
        },
        confidence: analysisResult.confidence || 0.85
      };
    } catch (error) {
      console.error('Real AI analysis failed:', error);
      throw new Error(`AI document analysis failed: ${error.message}`);
    }
  }

  /**
   * Real AI conversation analysis using OpenAI
   */
  private async performRealAIConversationAnalysis(input: string): Promise<ConversationAnalysisResult> {
    try {
      const systemPrompt = 'You are an expert business analyst specializing in requirements generation. Always return valid JSON.';

      const userPrompt = `
Convert the following user description into structured requirements.

User Input: ${input}

Please generate requirements in the following JSON format:
{
  "requirements": [
    {
      "title": "Brief requirement title",
      "description": "Detailed description",
      "type": "functional|non-functional|constraint|assumption",
      "priority": "low|medium|high|critical",
      "acceptance_criteria": "How to verify this requirement",
      "rationale": "Why this requirement is needed"
    }
  ],
  "suggestions": [
    "Specific suggestion for improvement",
    "Another helpful suggestion"
  ],
  "confidence": 0.92
}

Focus on:
1. Understanding user intent
2. Creating actionable requirements
3. Suggesting improvements
4. Identifying missing aspects

Return only valid JSON, no additional text.`;

      const messages: AIMessage[] = [
        { role: 'user', content: userPrompt }
      ];

      const response = await aiProviderService.generateResponse(messages, {
        systemPrompt,
        temperature: 0.4,
        maxTokens: 1500
      });

      const analysisResult = this.extractJsonFromResponse(response.content);

      console.log(`✅ ${response.provider} generated ${analysisResult.requirements?.length || 0} requirements from conversation`);

      return {
        requirements: analysisResult.requirements || [],
        suggestions: analysisResult.suggestions || [],
        confidence: analysisResult.confidence || 0.85
      };
    } catch (error) {
      console.error('Real AI conversation analysis failed:', error);
      throw new Error(`AI conversation analysis failed: ${error.message}`);
    }
  }

  /**
   * Mock document analysis (fallback when no AI key)
   */
  private async performMockDocumentAnalysis(content: string, fileName: string): Promise<DocumentAnalysisResult> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));

    const requirements = [];

    // Look for authentication-related content
    if (content.toLowerCase().includes('authentication') || content.toLowerCase().includes('login')) {
      requirements.push({
        title: "User Authentication System",
        description: "System must provide secure user authentication capabilities",
        type: "functional" as const,
        priority: "high" as const,
        acceptance_criteria: "Users can register, login, and manage their accounts securely",
        source: `Mock Analysis: ${fileName}`
      });
    }

    // Look for performance-related content
    if (content.toLowerCase().includes('performance') || content.toLowerCase().includes('response time')) {
      requirements.push({
        title: "System Performance Requirements",
        description: "System must meet specified performance criteria",
        type: "non-functional" as const,
        priority: "medium" as const,
        acceptance_criteria: "System responds within specified time limits under normal load",
        source: `Mock Analysis: ${fileName}`
      });
    }

    // Default requirement if no specific patterns found
    if (requirements.length === 0) {
      requirements.push({
        title: "General System Requirement",
        description: `Requirement extracted from ${fileName}`,
        type: "functional" as const,
        priority: "medium" as const,
        acceptance_criteria: "System meets the specified functional requirements",
        source: `Mock Analysis: ${fileName}`
      });
    }

    return {
      requirements,
      metadata: {
        documentType: this.detectDocumentType(fileName),
        language: "English",
        extractedAt: new Date().toISOString()
      },
      confidence: 0.75 // Lower confidence for mock
    };
  }

  /**
   * Mock conversation analysis (fallback when no AI key)
   */
  private async performMockConversationAnalysis(input: string): Promise<ConversationAnalysisResult> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));

    const requirements = [];
    const lowerInput = input.toLowerCase();

    // Analyze input for different types of requirements
    if (lowerInput.includes('user') && (lowerInput.includes('register') || lowerInput.includes('login') || lowerInput.includes('account'))) {
      requirements.push({
        title: "User Management System",
        description: "Users should be able to create and manage their accounts",
        type: "functional" as const,
        priority: "high" as const,
        acceptance_criteria: "Users can register, login, and update their profiles",
        rationale: "Essential for user identification and personalization"
      });
    }

    // Default requirement based on input
    if (requirements.length === 0) {
      requirements.push({
        title: "Core System Functionality",
        description: "System must provide the core functionality as described",
        type: "functional" as const,
        priority: "medium" as const,
        acceptance_criteria: "System meets the basic functional requirements",
        rationale: "Based on user input analysis"
      });
    }

    return {
      requirements,
      suggestions: [
        "Consider adding more specific acceptance criteria",
        "Think about edge cases and error handling",
        "Define measurable success metrics"
      ],
      confidence: 0.75 // Lower confidence for mock
    };
  }
}

export const aiAnalysisService = AIAnalysisService.getInstance();
