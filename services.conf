# Orbit Service Port Configuration
# Gateway is the single entry point on port 8080

GATEWAY_PORT=8080
DATABASE_URL=postgres://postgres:postgres@localhost:5432/appdb?sslmode=disable

# Backend Services
AUTH_SERVICE_PORT=8004
CRM_BACKEND_PORT=8001
PROJECTHUB_BACKEND_PORT=8003

# Frontend Services  
CRM_WEB_PORT=8081
PROJECTHUB_WEB_PORT=8082

# Service URLs for Gateway
AUTH_SERVICE_URL=http://localhost:8004
CRM_BACKEND_URL=http://localhost:8001
CRM_WEB_URL=http://localhost:8081
PROJECTHUB_BACKEND_URL=http://localhost:8003
PROJECTHUB_WEB_URL=http://localhost:8082