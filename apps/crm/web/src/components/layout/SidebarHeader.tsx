
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SidebarHeaderProps {
  closeSidebar: () => void;
}

export const SidebarHeader = ({ closeSidebar }: SidebarHeaderProps) => {
  return (
    <div className="flex h-16 items-center border-b border-gray-200 px-6">
      <a href="/dashboard" className="flex items-center gap-2" onClick={closeSidebar}>
        <img 
          src={`${window.location.pathname.startsWith('/crm/') ? '/crm' : ''}/symbol black-XXL.png`}
          alt="two dot ai logo" 
          className="h-8"
        />
      </a>
      <Button
        variant="ghost"
        size="icon"
        className="absolute right-4 top-4 lg:hidden"
        onClick={closeSidebar}
      >
        <X className="h-5 w-5" />
      </Button>
    </div>
  );
};
