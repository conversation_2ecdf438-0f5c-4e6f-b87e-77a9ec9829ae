import 'dotenv/config';
import express, { Request, Response } from 'express';
import cors from 'cors';
import passport from './config/passport.js';
import { PrismaClient } from '../generated/prisma/index.js';
import { RBACService } from './services/rbacService.js';
import { guestByDefault, authenticateToken, requirePermission } from './middleware/auth.js';
import { ResourceType, ActionType } from './types/index.js';

// Import routes
import authRoutes from './routes/auth.js';
import adminRoutes from './routes/admin.js';
import requirementsRoutes from './routes/requirements.js';
import aiAnalysisRoutes from './routes/ai-analysis.js';
import prdGatheringRoutes from './routes/prd/gathering.js';
import prdContentRoutes from './routes/prd/content.js';
import prdDocumentRoutes from './routes/prd/document.js';
import aiProvidersRoutes from './routes/ai-providers.js';

const app = express();
const port = process.env.PORT || 8003;
const prisma = new PrismaClient();

// Initialize RBAC system on startup
async function initializeApp() {
  try {
    await RBACService.initializeDefaultRoles();
    console.log('✅ RBAC system initialized');
  } catch (error) {
    console.error('❌ Failed to initialize RBAC system:', error);
  }
}

// Middleware
app.use(cors({
  origin: ['http://localhost:8080', 'http://localhost:8082'],
  credentials: true
}));
app.use(express.json());
app.use(passport.initialize());

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/requirements', requirementsRoutes);
app.use('/api/ai-analysis', aiAnalysisRoutes);

// PRD Routes
app.use('/api/prd/gathering', prdGatheringRoutes);
app.use('/api/prd/content', prdContentRoutes);
app.use('/api/prd/document', prdDocumentRoutes);

// AI Providers Routes
app.use('/api/ai-providers', aiProvidersRoutes);

// Health check endpoint
app.get('/api/health', (req: Request, res: Response) => {
  res.json({ status: 'ok', message: 'API server is running' });
});

// Projects endpoints (with guest-by-default access)
app.get('/api/projects', guestByDefault, async (req: Request, res: Response) => {
  try {
    const projects = await prisma.project.findMany({
      include: {
        tasks: {
          include: {
            sub_tasks: true,
            assignee_member: true
          },
          orderBy: { created_at: 'asc' }
        },
        project_lead_member: true,
        customer_lead_member: true,
        impact_type_ref: true
      },
      orderBy: { priority_order: 'asc' }
    });
    res.json(projects);
  } catch (error: any) {
    console.error('Error fetching projects:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/projects/basic', guestByDefault, async (req: Request, res: Response) => {
  try {
    const projects = await prisma.project.findMany({
      orderBy: { priority_order: 'asc' }
    });
    res.json(projects);
  } catch (error: any) {
    console.error('Error fetching basic projects:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/projects/:id', guestByDefault, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const project = await prisma.project.findUnique({
      where: { id },
      include: {
        tasks: {
          include: {
            sub_tasks: true,
            assignee_member: true
          },
          orderBy: { created_at: 'asc' }
        },
        project_lead_member: true,
        customer_lead_member: true,
        impact_type_ref: true
      }
    });
    
    if (!project) {
      return res.status(404).json({ error: 'Project not found' });
    }
    
    res.json(project);
  } catch (error: any) {
    console.error('Error fetching project:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/projects',
  authenticateToken,
  requirePermission(ResourceType.PROJECT, ActionType.CREATE),
  async (req: Request, res: Response) => {
    try {
      const project = await prisma.project.create({
        data: req.body
      });
      res.status(201).json(project);
    } catch (error: any) {
      console.error('Error creating project:', error);
      res.status(500).json({ error: error.message });
    }
  }
);

app.put('/api/projects/:id',
  authenticateToken,
  requirePermission(ResourceType.PROJECT, ActionType.UPDATE),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const project = await prisma.project.update({
        where: { id },
        data: req.body
      });
      res.json(project);
    } catch (error: any) {
      console.error('Error updating project:', error);
      res.status(500).json({ error: error.message });
    }
  }
);

app.delete('/api/projects/:id',
  authenticateToken,
  requirePermission(ResourceType.PROJECT, ActionType.DELETE),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      await prisma.project.delete({
        where: { id }
      });
      res.status(204).send();
    } catch (error: any) {
      console.error('Error deleting project:', error);
      res.status(500).json({ error: error.message });
    }
  }
);

// Team members endpoints
app.get('/api/team-members', guestByDefault, async (req: Request, res: Response) => {
  try {
    const { active } = req.query;
    const where = active === 'true' ? { is_active: true } : {};

    const teamMembers = await prisma.teamMember.findMany({
      where,
      orderBy: { name: 'asc' }
    });
    res.json(teamMembers);
  } catch (error: any) {
    console.error('Error fetching team members:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/team-members', async (req: Request, res: Response) => {
  try {
    const teamMember = await prisma.teamMember.create({
      data: req.body
    });
    res.status(201).json(teamMember);
  } catch (error: any) {
    console.error('Error creating team member:', error);
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/team-members/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const teamMember = await prisma.teamMember.update({
      where: { id },
      data: req.body
    });
    res.json(teamMember);
  } catch (error: any) {
    console.error('Error updating team member:', error);
    res.status(500).json({ error: error.message });
  }
});

app.delete('/api/team-members/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    await prisma.teamMember.delete({
      where: { id }
    });
    res.status(204).send();
  } catch (error: any) {
    console.error('Error deleting team member:', error);
    res.status(500).json({ error: error.message });
  }
});

// Impact types endpoints
app.get('/api/impact-types', async (req: Request, res: Response) => {
  try {
    const impactTypes = await prisma.impactTypes.findMany({
      where: { is_active: true },
      orderBy: { sort_order: 'asc' }
    });
    res.json(impactTypes);
  } catch (error: any) {
    console.error('Error fetching impact types:', error);
    res.status(500).json({ error: error.message });
  }
});

// Tasks endpoints
app.post('/api/tasks',
  authenticateToken,
  requirePermission(ResourceType.PROJECT, ActionType.UPDATE),
  async (req: Request, res: Response) => {
    try {
      const task = await prisma.task.create({
        data: req.body,
        include: {
          sub_tasks: true,
          assignee_member: true
        }
      });
      res.status(201).json(task);
    } catch (error: any) {
      console.error('Error creating task:', error);
      res.status(500).json({ error: error.message });
    }
  }
);

app.put('/api/tasks/:id',
  authenticateToken,
  requirePermission(ResourceType.PROJECT, ActionType.UPDATE),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const task = await prisma.task.update({
        where: { id },
        data: req.body,
        include: {
          sub_tasks: true,
          assignee_member: true
        }
      });
      res.json(task);
    } catch (error: any) {
      console.error('Error updating task:', error);
      res.status(500).json({ error: error.message });
    }
  }
);

app.delete('/api/tasks/:id',
  authenticateToken,
  requirePermission(ResourceType.PROJECT, ActionType.UPDATE),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      
      // First delete all sub-tasks
      await prisma.subTask.deleteMany({
        where: { task_id: id }
      });
      
      // Then delete the task
      await prisma.task.delete({
        where: { id }
      });
      
      res.status(204).send();
    } catch (error: any) {
      console.error('Error deleting task:', error);
      res.status(500).json({ error: error.message });
    }
  }
);

// Sub-tasks endpoints
app.post('/api/sub-tasks',
  authenticateToken,
  requirePermission(ResourceType.PROJECT, ActionType.UPDATE),
  async (req: Request, res: Response) => {
    try {
      const subTask = await prisma.subTask.create({
        data: req.body,
        include: {
          assignee_member: true
        }
      });
      res.status(201).json(subTask);
    } catch (error: any) {
      console.error('Error creating sub-task:', error);
      res.status(500).json({ error: error.message });
    }
  }
);

app.put('/api/sub-tasks/:id',
  authenticateToken,
  requirePermission(ResourceType.PROJECT, ActionType.UPDATE),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const subTask = await prisma.subTask.update({
        where: { id },
        data: req.body,
        include: {
          assignee_member: true
        }
      });
      res.json(subTask);
    } catch (error: any) {
      console.error('Error updating sub-task:', error);
      res.status(500).json({ error: error.message });
    }
  }
);

app.delete('/api/sub-tasks/:id',
  authenticateToken,
  requirePermission(ResourceType.PROJECT, ActionType.UPDATE),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      await prisma.subTask.delete({
        where: { id }
      });
      res.status(204).send();
    } catch (error: any) {
      console.error('Error deleting sub-task:', error);
      res.status(500).json({ error: error.message });
    }
  }
);

// Start the server
app.listen(port, async () => {
  console.log(`🚀 API server running at http://localhost:${port}`);
  await initializeApp();
});

export default app;
