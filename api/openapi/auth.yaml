openapi: 3.0.3
info:
  title: CRM Auth API
  version: 1.0.0
  description: Authentication endpoints for the CRM system

paths:
  /auth/session:
    get:
      summary: Get current session
      description: Retrieve the current user session
      responses:
        '200':
          description: Session information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Session'
        '401':
          description: Not authenticated

  /auth/signin:
    post:
      summary: Sign in with email/password
      description: Authenticate user with email and password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 6
              required:
                - email
                - password
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: Invalid credentials

  /auth/signin/oauth:
    post:
      summary: Sign in with OAuth
      description: Authenticate user with OAuth provider (Google)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider:
                  type: string
                  enum: [google]
                redirectTo:
                  type: string
                  format: uri
              required:
                - provider
      responses:
        '200':
          description: OAuth redirect URL
          content:
            application/json:
              schema:
                type: object
                properties:
                  url:
                    type: string
                    format: uri

  /auth/callback/{provider}:
    get:
      summary: OAuth callback
      description: Handle OAuth provider callback with authorization code
      parameters:
        - name: provider
          in: path
          required: true
          schema:
            type: string
            enum: [google]
        - name: code
          in: query
          required: true
          schema:
            type: string
        - name: state
          in: query
          required: true
          schema:
            type: string
        - name: error
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: OAuth authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: Invalid request or OAuth error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Authentication failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/signout:
    post:
      summary: Sign out
      description: End the current user session
      responses:
        '200':
          description: Successfully signed out
        '401':
          description: Not authenticated

  /auth/user:
    get:
      summary: Get current user
      description: Retrieve current authenticated user information
      responses:
        '200':
          description: User information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Not authenticated

components:
  schemas:
    Session:
      type: object
      properties:
        access_token:
          type: string
        refresh_token:
          type: string
        expires_in:
          type: integer
        token_type:
          type: string
        user:
          $ref: '#/components/schemas/User'

    AuthResponse:
      type: object
      properties:
        access_token:
          type: string
        refresh_token:
          type: string
        expires_in:
          type: integer
        token_type:
          type: string
        user:
          $ref: '#/components/schemas/User'

    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        email_confirmed_at:
          type: string
          format: date-time
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time