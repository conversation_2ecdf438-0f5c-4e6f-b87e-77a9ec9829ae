#!/bin/bash

# Development Environment Setup Script
# This script sets up the complete development environment using Docker

set -e

# Set up Bazel runfiles and copy files to working directory
# Check if we're running under <PERSON><PERSON> (either RUNFILES_DIR is set or we're in execroot)
if [[ -n "${RUNFILES_DIR:-}" ]] || [[ "$(pwd)" == *"execroot"* ]]; then
  # Running under Bazel - debug what's available
  echo "DEBUG: Running under Bazel"
  echo "DEBUG: RUNFILES_DIR=${RUNFILES_DIR:-'not set'}"
  echo "DEBUG: PWD=$(pwd)"
  
  # Find runfiles directory if not set
  if [[ -z "${RUNFILES_DIR:-}" ]]; then
    # Look for runfiles in common locations
    POSSIBLE_RUNFILES=$(find /tmp -name "*runfiles*" -type d 2>/dev/null | head -1)
    if [[ -n "$POSSIBLE_RUNFILES" ]]; then
      export RUNFILES_DIR="$POSSIBLE_RUNFILES"
      echo "DEBUG: Found runfiles at $RUNFILES_DIR"
    fi
  fi
  
  if [[ -n "${RUNFILES_DIR:-}" ]]; then
    echo "DEBUG: Available files in runfiles:"
    find "$RUNFILES_DIR" -name "*.yml" -o -name "*.yaml" -o -name "*.conf" -o -name "*.sql" | head -20
  fi
  
  # Create working directory in current location (outside sandbox)
  WORK_DIR="/tmp/orbit-bazel-$$"
  mkdir -p "$WORK_DIR"
  echo "Setting up working directory: $WORK_DIR"
  
  # Try to find and copy the docker-compose files
  if [[ -f "$RUNFILES_DIR/_main/docker-compose.dev.yml" ]]; then
    cp "$RUNFILES_DIR/_main/docker-compose.dev.yml" "$WORK_DIR/"
    echo "Copied docker-compose.dev.yml"
  else
    echo "docker-compose.dev.yml not found, looking in current directory"
    if [[ -f "docker-compose.dev.yml" ]]; then
      cp "docker-compose.dev.yml" "$WORK_DIR/"
    else
      echo "ERROR: docker-compose.dev.yml not found anywhere"
      exit 1
    fi
  fi
  
  # Copy nginx config and Dockerfile if available
  mkdir -p "$WORK_DIR/gateway"
  if [[ -f "$RUNFILES_DIR/_main/gateway/nginx-dev.conf" ]]; then
    cp "$RUNFILES_DIR/_main/gateway/nginx-dev.conf" "$WORK_DIR/gateway/"
  else
    # Fall back to current directory
    cp "gateway/nginx-dev.conf" "$WORK_DIR/gateway/" 2>/dev/null || echo "nginx-dev.conf not available"
  fi
  
  if [[ -f "$RUNFILES_DIR/_main/gateway/Dockerfile" ]]; then
    cp "$RUNFILES_DIR/_main/gateway/Dockerfile" "$WORK_DIR/gateway/"
  else
    # Fall back to current directory
    cp "gateway/Dockerfile" "$WORK_DIR/gateway/" 2>/dev/null || echo "gateway Dockerfile not available"
  fi
  
  if [[ -f "$RUNFILES_DIR/_main/gateway/nginx.conf" ]]; then
    cp "$RUNFILES_DIR/_main/gateway/nginx.conf" "$WORK_DIR/gateway/"
  else
    # Fall back to current directory
    cp "gateway/nginx.conf" "$WORK_DIR/gateway/" 2>/dev/null || echo "nginx.conf not available"
  fi
  
  # Copy seed data if available  
  mkdir -p "$WORK_DIR/infra/database"
  if [[ -f "$RUNFILES_DIR/_main/infra/database/container_seed_data.sql" ]]; then
    cp "$RUNFILES_DIR/_main/infra/database/container_seed_data.sql" "$WORK_DIR/infra/database/"
  else
    # Fall back to current directory
    cp "infra/database/container_seed_data.sql" "$WORK_DIR/infra/database/" 2>/dev/null || echo "container_seed_data.sql not available"
  fi
  
  cd "$WORK_DIR"
  
  # Cleanup function
  cleanup() {
    echo "Cleaning up temporary directory: $WORK_DIR"
    rm -rf "$WORK_DIR"
  }
  trap cleanup EXIT
else
  # Running directly
  cd "$(dirname "$0")/.."
fi

echo "=== Orbit Development Environment Setup ==="
echo ""

# Stop any existing services
echo "1. Stopping any existing services..."
docker-compose down 2>/dev/null || true
docker-compose -f docker-compose.dev.yml down 2>/dev/null || true

# Start database and gateway
echo "2. Starting database and gateway..."
echo "Working directory: $(pwd)"
echo "Files available:"
ls -la
docker-compose -f docker-compose.dev.yml up -d postgres gateway

# Wait for database to be ready
echo "3. Waiting for database to be ready..."
sleep 10

# Test database connection
echo "4. Testing database connection..."
if PGPASSWORD=orbit_password psql -h localhost -p 5432 -U orbit_user -d orbit -c "SELECT 'Database ready!' as status;" > /dev/null 2>&1; then
    echo "✅ Database is ready!"
else
    echo "❌ Database connection failed"
    exit 1
fi

# Test gateway
echo "5. Testing gateway..."
if curl -s http://localhost:8080/health | grep -q "healthy"; then
    echo "✅ Gateway is ready!"
else
    echo "❌ Gateway health check failed"
    exit 1
fi

echo ""
echo "🎉 Development environment is ready!"
echo ""
echo "Next steps:"
echo "  1. Run backend services:"
echo "     bazel run //services/auth:auth_service &"
echo "     bazel run //apps/crm/backend:crm_backend &"
echo "     bazel run //apps/project-hub/backend:project_hub_backend &"
echo ""
echo "  2. Run frontend services:"
echo "     bazel run //apps/crm/web:dev &"
echo "     bazel run //apps/project-hub/frontend:dev &"
echo ""
echo "  3. Access the application:"
echo "     CRM: http://localhost:8080/crm/"
echo "     ProjectHub: http://localhost:8080/hub/"
echo ""
echo "Database connection details:"
echo "  Host: localhost"
echo "  Port: 5432"
echo "  Database: orbit"
echo "  User: orbit_user"
echo "  Password: orbit_password"