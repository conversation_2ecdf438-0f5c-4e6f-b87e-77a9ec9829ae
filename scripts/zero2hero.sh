#!/bin/bash

# Zero to Hero - Complete Orbit Development Environment Setup
# This script does everything from scratch: build, deploy, test, and validate

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Orbit ASCII Art Logo
show_logo() {
    echo -e "${PURPLE}"
    echo "   ██████  ██████  ██████  ██ ████████ "
    echo "  ██    ██ ██   ██ ██   ██ ██    ██    "
    echo "  ██    ██ ██████  ██████  ██    ██    "
    echo "  ██    ██ ██   ██ ██   ██ ██    ██    "
    echo "   ██████  ██   ██ ██████  ██    ██    "
    echo -e "${NC}"
    echo -e "${WHITE}🚀 ZERO TO HERO - Complete Development Environment${NC}"
    echo -e "${CYAN}═══════════════════════════════════════════════════${NC}"
    echo ""
}

# Progress indicator
progress() {
    local step=$1
    local total=$2
    local message=$3
    local percentage=$((step * 100 / total))
    
    echo -e "${BLUE}[${step}/${total}] ${percentage}% ${message}${NC}"
}

# Success checkmark
success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Error with X mark
error() {
    echo -e "${RED}❌ $1${NC}"
}

# Warning with warning sign
warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Info with info sign
info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

# Set up Bazel runfiles and copy files to working directory
if [[ -n "${RUNFILES_DIR:-}" ]] || [[ "$(pwd)" == *"execroot"* ]]; then
    # Running under Bazel
    info "Running under Bazel environment"
    
    # Create working directory in current location (outside sandbox)
    WORK_DIR="/tmp/orbit-zero2hero-$$"
    mkdir -p "$WORK_DIR"
    info "Working directory: $WORK_DIR"
    
    # Copy required files if available
    if [[ -n "${RUNFILES_DIR:-}" ]]; then
        find "$RUNFILES_DIR" -name "docker-compose*.yml" -exec cp {} "$WORK_DIR/" \; 2>/dev/null || true
        find "$RUNFILES_DIR" -name "*.conf" -exec mkdir -p "$WORK_DIR/gateway" \; -exec cp {} "$WORK_DIR/gateway/" \; 2>/dev/null || true
        find "$RUNFILES_DIR" -name "Dockerfile" -exec mkdir -p "$WORK_DIR/gateway" \; -exec cp {} "$WORK_DIR/gateway/" \; 2>/dev/null || true
        find "$RUNFILES_DIR" -name "container_seed_data.sql" -exec mkdir -p "$WORK_DIR/infra/database" \; -exec cp {} "$WORK_DIR/infra/database/" \; 2>/dev/null || true
    fi
    
    # Fallback: copy from original location if files not found
    if [[ ! -f "$WORK_DIR/docker-compose.dev.yml" ]]; then
        ORIGINAL_DIR="$(pwd)"
        
        # Try to find the original orbit directory
        if [[ "$ORIGINAL_DIR" == *"execroot"* ]]; then
            # Look for the real orbit directory
            ORBIT_DIR="/Users/<USER>/workspaces/git/orbit"
            if [[ -d "$ORBIT_DIR" ]]; then
                cd "$ORBIT_DIR"
                cp docker-compose*.yml "$WORK_DIR/" 2>/dev/null || true
                mkdir -p "$WORK_DIR/gateway"
                cp gateway/*.conf "$WORK_DIR/gateway/" 2>/dev/null || true
                cp gateway/Dockerfile "$WORK_DIR/gateway/" 2>/dev/null || true
                mkdir -p "$WORK_DIR/infra/database"
                cp infra/database/container_seed_data.sql "$WORK_DIR/infra/database/" 2>/dev/null || true
            fi
        else
            cd "$(dirname "$0")/.." 2>/dev/null || true
            cp docker-compose*.yml "$WORK_DIR/" 2>/dev/null || true
            mkdir -p "$WORK_DIR/gateway"
            cp gateway/*.conf "$WORK_DIR/gateway/" 2>/dev/null || true
            cp gateway/Dockerfile "$WORK_DIR/gateway/" 2>/dev/null || true
            mkdir -p "$WORK_DIR/infra/database"
            cp infra/database/container_seed_data.sql "$WORK_DIR/infra/database/" 2>/dev/null || true
        fi
        
        cd "$WORK_DIR"
    fi
    
    cd "$WORK_DIR"
    
    # Note: Cleanup will happen at the very end
else
    # Running directly
    cd "$(dirname "$0")/.."
fi

show_logo

# Step 1: Clean slate
progress 1 10 "🧹 Creating clean slate environment"
echo "Stopping any existing services..."
# Stop Docker services
docker stop $(docker ps -q --filter "name=orbit-*") 2>/dev/null || true
docker rm $(docker ps -aq --filter "name=orbit-*") 2>/dev/null || true
docker-compose down 2>/dev/null || true
docker-compose -f docker-compose.dev.yml down -v 2>/dev/null || true

# Stop local services
for port in 8003 8004 8005 8081 8082; do
    PID=$(lsof -ti:$port 2>/dev/null || echo "")
    if [[ -n "$PID" ]] && [[ "$PID" != "" ]]; then
        kill $PID 2>/dev/null || true
        echo "Stopped service on port $port (PID: $PID)"
    fi
done
success "Clean slate prepared - all services stopped"

# Step 2: Build all services
progress 2 10 "🔨 Building all services with Bazel"
echo "Building platform services..."
if command -v bazel >/dev/null 2>&1; then
    ORIGINAL_PWD="$(pwd)"
    cd "$(dirname "$0")/.." 2>/dev/null || true
    bazel build //platform/... 2>/dev/null || warning "Some platform builds may have failed (this is OK for Docker-only setup)"
    bazel build //services/... 2>/dev/null || warning "Some service builds may have failed (this is OK for local dev)"
    bazel build //apps/... 2>/dev/null || warning "Some app builds may have failed (this is OK for local dev)"
    cd "$ORIGINAL_PWD" 2>/dev/null || true
else
    warning "Bazel not found in PATH - skipping build phase"
fi
success "Build phase completed"

# Step 3: Start infrastructure
progress 3 10 "🐳 Starting Docker infrastructure"
echo "Starting PostgreSQL database and nginx gateway..."
docker-compose -f docker-compose.dev.yml up -d postgres gateway
success "Infrastructure services started"

# Step 4: Wait for database
progress 4 10 "⏳ Waiting for database initialization"
echo "Waiting for PostgreSQL to initialize with seed data..."
sleep 45

# Test database connection with retries
for i in {1..5}; do
    if PGPASSWORD=orbit_password psql -h localhost -p 5432 -U orbit_user -d orbit -c "SELECT 'Database ready!' as status;" > /dev/null 2>&1; then
        success "Database is ready and contains $(PGPASSWORD=orbit_password psql -h localhost -p 5432 -U orbit_user -d orbit -c "SELECT COUNT(*) FROM users;" -t | xargs) users"
        break
    else
        if [ $i -eq 5 ]; then
            error "Database failed to start after 5 attempts"
            exit 1
        fi
        echo "Attempt $i/5: Database not ready, waiting 5 more seconds..."
        sleep 5
    fi
done

# Import comprehensive seed data for ProjectHub functionality
echo "Importing comprehensive seed data for ProjectHub..."
cd "$(dirname "$0")/.." 2>/dev/null || true
if [ -f "infra/database/comprehensive_seed_data.sql" ]; then
    PGPASSWORD=orbit_password psql -h localhost -p 5432 -U orbit_user -d orbit -f infra/database/comprehensive_seed_data.sql > /dev/null 2>&1
    PROJECT_COUNT=$(PGPASSWORD=orbit_password psql -h localhost -p 5432 -U orbit_user -d orbit -c "SELECT COUNT(*) FROM projects;" -t 2>/dev/null | xargs || echo "0")
    if [ "$PROJECT_COUNT" -gt "0" ]; then
        success "Comprehensive seed data imported - ProjectHub has $PROJECT_COUNT projects"
    else
        warning "ProjectHub tables may not be available - some features might be limited"
    fi
else
    warning "Comprehensive seed data file not found - ProjectHub may have limited functionality"
fi
cd "$WORK_DIR" 2>/dev/null || true

# Step 5: Verify gateway
progress 5 10 "🌐 Verifying gateway health"
for i in {1..3}; do
    if curl -s http://localhost:8080/health | grep -q "healthy"; then
        success "Gateway is responding: $(curl -s http://localhost:8080/health)"
        break
    else
        if [ $i -eq 3 ]; then
            error "Gateway failed to respond"
            exit 1
        fi
        echo "Attempt $i/3: Gateway not ready, waiting..."
        sleep 3
    fi
done

# Step 6: Start backend services
progress 6 10 "🚀 Starting backend services"
BACKEND_COUNT=0

# Start services only if they're not already running
if ! lsof -i :8004 >/dev/null 2>&1; then
    echo "Starting Auth Service on port 8004..."
    cd "$(dirname "$0")/.." 2>/dev/null || true
    export DATABASE_URL="postgres://orbit_user:orbit_password@localhost:5432/orbit?sslmode=disable"
    export JWT_SECRET="dev-jwt-secret-key"
    nohup bazel run //services/auth:auth > auth.log 2>&1 &
    AUTH_PID=$!
    echo "Auth Service started with PID: $AUTH_PID"
    cd "$WORK_DIR" 2>/dev/null || true
    sleep 3
else
    success "Auth Service already running on port 8004"
fi

if ! lsof -i :8005 >/dev/null 2>&1; then
    echo "Starting CRM Backend on port 8005..."
    cd "$(dirname "$0")/.." 2>/dev/null || true
    export DATABASE_URL="postgres://orbit_user:orbit_password@localhost:5432/orbit?sslmode=disable"
    export PORT="8005"
    nohup bazel run //apps/crm/backend:crm_backend > crm_backend.log 2>&1 &
    CRM_PID=$!
    echo "CRM Backend started with PID: $CRM_PID"
    cd "$WORK_DIR" 2>/dev/null || true
    sleep 3
else
    success "CRM Backend already running on port 8005"
fi

# Start ProjectHub services
if ! lsof -i :8003 >/dev/null 2>&1; then
    echo "Starting ProjectHub Backend and Frontend..."
    cd "$(dirname "$0")/../apps/project-hub" 2>/dev/null || true
    if [ -f "package.json" ]; then
        export DATABASE_URL="postgres://orbit_user:orbit_password@localhost:5432/orbit?sslmode=disable"
        export PORT="8003"
        export VITE_API_URL="http://localhost:8080/api"
        nohup npm run dev > ../../project_hub.log 2>&1 &
        HUB_PID=$!
        echo "ProjectHub services started with PID: $HUB_PID"
        cd "$WORK_DIR" 2>/dev/null || true
        sleep 3
    else
        warning "ProjectHub package.json not found, skipping"
    fi
else
    success "ProjectHub already running on port 8003"
fi

# Wait a bit for services to start
echo "Waiting for backend services to initialize..."
sleep 30

# Check what's actually running now
if curl -s http://localhost:8003/api/health | grep -q "ok"; then
    success "ProjectHub Backend running on port 8003"
    BACKEND_COUNT=$((BACKEND_COUNT + 1))
fi

if lsof -i :8004 >/dev/null 2>&1; then
    success "Auth Service running on port 8004"
    BACKEND_COUNT=$((BACKEND_COUNT + 1))
fi

if lsof -i :8005 >/dev/null 2>&1; then
    success "CRM Backend running on port 8005"
    BACKEND_COUNT=$((BACKEND_COUNT + 1))
fi

success "$BACKEND_COUNT/3 backend services are now running"

# Step 7: Start frontend services  
progress 7 10 "🎨 Starting frontend services"
FRONTEND_COUNT=0

# Start services only if they're not already running
if ! lsof -i :8081 >/dev/null 2>&1; then
    echo "Starting CRM Frontend on port 8081..."
    cd "$(dirname "$0")/.." 2>/dev/null || true
    nohup bazel run //apps/crm/web:dev > crm_web.log 2>&1 &
    CRM_WEB_PID=$!
    echo "CRM Frontend started with PID: $CRM_WEB_PID"
    cd "$WORK_DIR" 2>/dev/null || true
    sleep 5
else
    success "CRM Frontend already running on port 8081"
fi



# Wait a bit for frontend services to start
echo "Waiting for frontend services to initialize..."
sleep 45

# Check what's actually running now
if lsof -i :8081 >/dev/null 2>&1; then
    success "CRM Frontend running on port 8081"
    FRONTEND_COUNT=$((FRONTEND_COUNT + 1))
fi

if lsof -i :8082 >/dev/null 2>&1; then
    success "ProjectHub Frontend running on port 8082"
    FRONTEND_COUNT=$((FRONTEND_COUNT + 1))
fi

success "$FRONTEND_COUNT/2 frontend services are now running"

# Step 8: Test API endpoints
progress 8 10 "🔌 Testing API endpoints"

# Test gateway routing
if curl -s http://localhost:8080/health | grep -q "healthy"; then
    success "Gateway health endpoint working"
else
    error "Gateway health endpoint failed"
fi

# Test frontend routing
if curl -s http://localhost:8080/crm/ | grep -q "<!DOCTYPE html>"; then
    success "CRM frontend serving HTML"
else
    warning "CRM frontend may not be accessible"
fi

if curl -s http://localhost:8080/hub/ | grep -q "<!DOCTYPE html>"; then
    success "ProjectHub frontend serving HTML"
else
    warning "ProjectHub frontend may not be accessible"
fi

# Test backend API endpoints
AUTH_RESPONSE=$(curl -s http://localhost:8080/api/v1/auth/session 2>/dev/null || echo "failed")
if [[ "$AUTH_RESPONSE" == *"error"* ]]; then
    success "Auth API responding (authentication required)"
else
    warning "Auth API may not be responding correctly: $AUTH_RESPONSE"
fi

CRM_RESPONSE=$(curl -s http://localhost:8080/api/v1/companies 2>/dev/null || echo "failed")
if [[ "$CRM_RESPONSE" == *"error"* ]]; then
    success "CRM API responding (authentication required)"
else
    warning "CRM API may not be responding correctly: $CRM_RESPONSE"
fi

# Step 9: Test login flow
progress 9 10 "🔐 Testing authentication flow"

# Test with seeded user credentials
LOGIN_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"TestPassword"}' \
    http://localhost:8080/api/v1/auth/signin 2>/dev/null || echo '{"error":"connection_failed"}')

if [[ "$LOGIN_RESPONSE" == *"token"* ]]; then
    success "Login successful with test credentials!"
    echo "Response: $LOGIN_RESPONSE"
elif [[ "$LOGIN_RESPONSE" == *"Database error"* ]]; then
    warning "Login endpoint working but database may need time to initialize"
    info "Database is now on standard port 5432"
elif [[ "$LOGIN_RESPONSE" == *"404"* ]]; then
    warning "Login endpoint not found - auth service may need route configuration"
else
    info "Login test response: $LOGIN_RESPONSE"
fi

# Step 10: Show final status dashboard
progress 10 10 "📊 Generating status dashboard"

echo ""
echo -e "${WHITE}═══════════════════════════════════════════════════${NC}"
echo -e "${WHITE}🎉 ZERO TO HERO SETUP COMPLETE!${NC}"
echo -e "${WHITE}═══════════════════════════════════════════════════${NC}"
echo ""

# Show service status
echo -e "${CYAN}🔧 Infrastructure Services:${NC}"
echo "  ✅ PostgreSQL Database: localhost:5432 (Docker)"
echo "  ✅ nginx Gateway: localhost:8080 (Docker)"
echo ""

echo -e "${CYAN}🎨 Frontend Applications:${NC}"
if [ $FRONTEND_COUNT -gt 0 ]; then
    echo "  ✅ CRM Web App: http://localhost:8080/crm/"
    echo "  ✅ ProjectHub: http://localhost:8080/hub/"
else
    echo "  ⚠️  Frontend services need to be started manually"
fi
echo ""

echo -e "${CYAN}⚙️  Backend Services:${NC}"
if [ $BACKEND_COUNT -gt 0 ]; then
    if lsof -i :8004 >/dev/null 2>&1; then
        echo "  ✅ Auth Service: localhost:8004"
    fi
    if lsof -i :8005 >/dev/null 2>&1; then
        echo "  ✅ CRM Backend: localhost:8005"
    fi
    if lsof -i :8003 >/dev/null 2>&1; then
        echo "  ✅ ProjectHub Backend: localhost:8003"
    fi
else
    echo "  ⚠️  Backend services need to be started manually"
fi
echo ""

echo -e "${CYAN}🗄️  Database Content:${NC}"
USER_COUNT=$(PGPASSWORD=orbit_password psql -h localhost -p 5432 -U orbit_user -d orbit -c "SELECT COUNT(*) FROM users;" -t 2>/dev/null | xargs || echo "0")
COMPANY_COUNT=$(PGPASSWORD=orbit_password psql -h localhost -p 5432 -U orbit_user -d orbit -c "SELECT COUNT(*) FROM companies;" -t 2>/dev/null | xargs || echo "0")
DEAL_COUNT=$(PGPASSWORD=orbit_password psql -h localhost -p 5432 -U orbit_user -d orbit -c "SELECT COUNT(*) FROM deals;" -t 2>/dev/null | xargs || echo "0")

echo "  📊 Users: $USER_COUNT"
echo "  🏢 Companies: $COMPANY_COUNT" 
echo "  💼 Deals: $DEAL_COUNT"
echo ""

echo -e "${CYAN}🧪 Test Credentials:${NC}"
echo "  📧 Email: <EMAIL>"
echo "  🔑 Password: TestPassword"
echo "  📧 Email: <EMAIL>"
echo "  🔑 Password: password123"
echo "  📧 Email: <EMAIL>"
echo "  🔑 Password: admin123"
echo ""

echo -e "${CYAN}🚀 Quick Start Commands:${NC}"
echo "  🛑 Stop all: bazel run //:stop_dev_environment"
echo "  ▶️  Start again: bazel run //:start_dev_environment"
echo "  🔄 Zero to Hero: bazel run //:zero2hero"
echo ""

echo -e "${CYAN}🌐 Access Points:${NC}"
echo "  🏠 Main App: http://localhost:8080/"
echo "  💼 CRM: http://localhost:8080/crm/"
echo "  📊 ProjectHub: http://localhost:8080/hub/"
echo "  🔍 Health Check: http://localhost:8080/health"
echo ""

if [ $BACKEND_COUNT -eq 3 ] && [ $FRONTEND_COUNT -eq 2 ]; then
    echo -e "${GREEN}🎉 ALL SYSTEMS GO! Your Orbit development environment is fully operational!${NC}"
    echo -e "${GREEN}   All 5 services started automatically and are running smoothly.${NC}"
else
    echo -e "${YELLOW}⚠️  Setup partially complete. Some services failed to start automatically.${NC}"
    if [ $BACKEND_COUNT -lt 3 ]; then
        echo -e "${YELLOW}   Backend services: $BACKEND_COUNT/3 running${NC}"
        echo -e "${YELLOW}   Check logs: auth.log, crm_backend.log, project_hub.log${NC}"
    fi
    if [ $FRONTEND_COUNT -lt 2 ]; then
        echo -e "${YELLOW}   Frontend services: $FRONTEND_COUNT/2 running${NC}"  
        echo -e "${YELLOW}   Check logs: crm_web.log, project_hub_frontend.log${NC}"
    fi
    echo -e "${CYAN}   💡 Tip: Services may take a few minutes to fully start up${NC}"
fi

echo ""
echo -e "${PURPLE}Made with ❤️  by the Orbit team${NC}"
echo -e "${WHITE}═══════════════════════════════════════════════════${NC}"

# Cleanup temp directory if we created one
if [[ -n "${WORK_DIR:-}" ]] && [[ "$WORK_DIR" == "/tmp/orbit-zero2hero-"* ]]; then
    info "Cleaning up temporary directory: $WORK_DIR"
    rm -rf "$WORK_DIR"
fi