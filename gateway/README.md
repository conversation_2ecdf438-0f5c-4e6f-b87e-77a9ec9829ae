# Gateway Service

This directory contains the Docker-based nginx gateway configuration for the Orbit project. The gateway serves as a reverse proxy that routes requests to the appropriate services.

## Overview

The gateway provides:
- **Route Management**: Routes `/crm/` to CRM frontend, `/hub/` to ProjectHub frontend
- **API Proxying**: Routes API calls to appropriate backend services
- **Static Assets**: Handles asset serving for both frontends with proper path resolution
- **Development Support**: WebSocket support for Vite HMR and development asset serving

## Configuration

### nginx.conf
The main nginx configuration file that defines:
- Port 8080 for the gateway
- Route mappings for all services
- Static asset handling with path stripping
- WebSocket support for development
- Health check endpoint at `/health`

### Key Routes
- `/` → Redirects to `/crm/` (CRM as default)
- `/crm/` → CRM Frontend (port 8081)
- `/hub/` → ProjectHub Frontend (port 8082)
- `/api/v1/auth/` → Auth Service (port 8004)
- `/api/v1/` → CRM Backend (port 8005)
- `/api/projects`, `/api/team-members`, `/api/impact-types` → ProjectHub Backend (port 8003)

### Static Assets
- CRM assets: `/crm/*.{png,jpg,svg,etc}` → CRM Frontend
- ProjectHub assets: `/hub/*.{png,jpg,svg,etc}` → ProjectHub Frontend
- Development assets: `/@vite/`, `/@fs/`, `/src/`, `/node_modules/` → Development servers

## Usage

### Development Mode
```bash
# Start only gateway and database
docker-compose -f docker-compose.dev.yml up postgres gateway

# Run services locally with Bazel (in separate terminals)
bazel run //services/auth:auth_service          # Port 8004
bazel run //apps/crm/backend:crm_backend        # Port 8005  
bazel run //apps/project-hub/backend:project_hub_backend  # Port 8003
bazel run //apps/crm/web:dev                    # Port 8081
bazel run //apps/project-hub/frontend:dev       # Port 8082
```

### Production Mode
```bash
# Start all services with Docker
docker-compose up --build
```

### Access Points
- **Gateway**: http://localhost:8080
- **CRM**: http://localhost:8080/crm/
- **ProjectHub**: http://localhost:8080/hub/
- **Health Check**: http://localhost:8080/health

## File Structure
```
gateway/
├── nginx.conf          # nginx configuration
├── Dockerfile          # Docker image configuration
└── README.md           # This file
```

## Troubleshooting

### Common Issues

1. **Static assets not loading**: Check that the service is running on the expected port
2. **WebSocket connection failed**: Ensure development servers are running locally
3. **502 Bad Gateway**: Verify that backend services are accessible

### Testing Routes
```bash
# Test health check
curl http://localhost:8080/health

# Test static assets
curl -I http://localhost:8080/hub/two-dot-ai-logo.png
curl -I http://localhost:8080/crm/symbol%20black-XXL.png

# Test API routes
curl http://localhost:8080/api/v1/auth/session
curl http://localhost:8080/api/projects
```

## Configuration Changes

When modifying nginx.conf:
1. Update the configuration file
2. Restart the gateway container: `docker-compose restart gateway`
3. Or rebuild: `docker-compose up --build gateway`

The configuration is now version controlled, making it easy for team members to get the same gateway setup.