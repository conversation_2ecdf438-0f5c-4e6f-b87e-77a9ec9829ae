package handlers

import (
	"database/sql"
	"time"

	"github.com/google/uuid"
	"github.com/oapi-codegen/runtime/types"
)

// Helper functions for pointer conversions

func stringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

func boolPtr(b bool) *bool {
	return &b
}

func timePtr(t time.Time) *time.Time {
	if t.IsZero() {
		return nil
	}
	return &t
}

func uuidPtr(s string) *types.UUID {
	if s == "" {
		return nil
	}
	u, err := uuid.Parse(s)
	if err != nil {
		return nil
	}
	uid := types.UUID(u)
	return &uid
}

func nullStringToPtr(ns sql.NullString) *string {
	if ns.Valid && ns.String != "" {
		return &ns.String
	}
	return nil
}

func nullStringToUUIDPtr(ns sql.NullString) *types.UUID {
	if ns.Valid && ns.String != "" {
		return uuidPtr(ns.String)
	}
	return nil
}

func ptrToString(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func ptrToUUID(u *types.UUID) string {
	if u == nil {
		return ""
	}
	return u.String()
}

func emailPtr(s string) *types.Email {
	if s == "" {
		return nil
	}
	e := types.Email(s)
	return &e
}

func intPtr(i int32) *int {
	val := int(i)
	return &val
}

func datePtr(s string) *types.Date {
	if s == "" {
		return nil
	}
	// Parse the string using the DateFormat - types.Date wraps time.Time
	parsed, err := time.Parse("2006-01-02", s)
	if err != nil {
		return nil
	}
	d := types.Date{Time: parsed}
	return &d
}