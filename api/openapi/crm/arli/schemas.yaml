# Document/Arli schemas
Document:
  type: object
  properties:
    id:
      type: string
      format: uuid
    content:
      type: string
    metadata:
      type: object
    filter_tags:
      type: array
      items:
        type: string
    source_id:
      type: string
    created_at:
      type: string
      format: date-time
    updated_at:
      type: string
      format: date-time

DocumentDetails:
  allOf:
    - $ref: '#/Document'
    - type: object
      properties:
        vector_embeddings:
          type: array
          items:
            type: number
        processing_status:
          type: string
          enum: [pending, processing, completed, failed]

DocumentCreate:
  type: object
  properties:
    content:
      type: string
      minLength: 1
    metadata:
      type: object
    filter_tags:
      type: array
      items:
        type: string
    source_id:
      type: string
  required:
    - content

DocumentUpdate:
  type: object
  properties:
    content:
      type: string
      minLength: 1
    metadata:
      type: object
    filter_tags:
      type: array
      items:
        type: string
    source_id:
      type: string

FilterTag:
  type: object
  properties:
    id:
      type: string
      format: uuid
    name:
      type: string
    color:
      type: string
      pattern: "^#[0-9a-fA-F]{6}$"
    created_at:
      type: string
      format: date-time

FilterTagCreate:
  type: object
  properties:
    name:
      type: string
      minLength: 1
    color:
      type: string
      pattern: "^#[0-9a-fA-F]{6}$"
      default: "#3b82f6"
  required:
    - name