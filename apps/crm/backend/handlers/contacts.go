package handlers

import (
	"database/sql"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/TwoDotAi/orbit/apps/crm/backend/database"
	"github.com/TwoDotAi/orbit/libs/go/logging"
	openapi "github.com/TwoDotAi/orbit/api/openapi"
	"github.com/oapi-codegen/runtime/types"
	"go.uber.org/zap"
)

// Use generated OpenAPI types
type Contact = openapi.Contact
type ContactWithCompany = openapi.ContactWithCompany
type ContactDetails = openapi.ContactDetails
type ContactCreate = openapi.ContactCreate
type ContactUpdate = openapi.ContactUpdate

func ListContacts(c *gin.Context) {
	db := database.GetDB()
	
	companyID := c.Query("company_id")
	unlinked := c.Query("unlinked") == "true"
	includeDeleted := c.Query("include_deleted") == "true"
	
	query := `SELECT c.id, c.first_name, c.last_name, c.email, c.phone, c.job_title, 
			         c.company_id, c.created_by, c.is_deleted, c.created_at, c.updated_at,
			         comp.id, comp.name, comp.is_deleted
			  FROM contacts c
			  LEFT JOIN companies comp ON c.company_id = comp.id
			  WHERE 1=1`
	args := []interface{}{}
	argIndex := 1

	if companyID != "" {
		query += " AND c.company_id = $" + string(rune(argIndex+'0'))
		args = append(args, companyID)
		argIndex++
	}

	if unlinked {
		query += " AND c.company_id IS NULL"
	}

	if !includeDeleted {
		query += " AND c.is_deleted = false"
	}

	query += " ORDER BY c.created_at DESC"

	rows, err := db.Query(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	defer rows.Close()

	var contacts []ContactWithCompany
	for rows.Next() {
		var contact ContactWithCompany
		var email, phone, jobTitle sql.NullString
		var companyID, companyName sql.NullString
		var companyIsDeleted sql.NullBool

		err := rows.Scan(&contact.Id, &contact.FirstName, &contact.LastName, &email,
			&phone, &jobTitle, &contact.CompanyId, &contact.CreatedBy,
			&contact.IsDeleted, &contact.CreatedAt, &contact.UpdatedAt,
			&companyID, &companyName, &companyIsDeleted)
		if err != nil {
			logging.LogError(err, "Failed to scan contact row",
				zap.String("operation", "list_contacts"),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}

		// Convert sql.NullString to pointer types for OpenAPI
		contact.Email = func() *types.Email {
			if email.Valid && email.String != "" {
				e := types.Email(email.String)
				return &e
			}
			return nil
		}()
		contact.Phone = nullStringToPtr(phone)
		contact.JobTitle = nullStringToPtr(jobTitle)

		if companyID.Valid {
			contact.Company = &openapi.CompanyInfo{
				Id:        nullStringToUUIDPtr(companyID),
				Name:      nullStringToPtr(companyName),
				IsDeleted: func() *bool {
					if companyIsDeleted.Valid {
						return boolPtr(companyIsDeleted.Bool)
					}
					return nil
				}(),
			}
		}

		contacts = append(contacts, contact)
	}

	c.JSON(http.StatusOK, contacts)
}

func CreateContact(c *gin.Context) {
	userID, _ := c.Get("user_id")
	
	var req ContactCreate
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	db := database.GetDB()
	
	id := uuid.New().String()
	now := time.Now()
	
	contact := Contact{
		Id:        uuidPtr(id),
		FirstName: stringPtr(req.FirstName),
		LastName:  stringPtr(req.LastName),
		Email:     func() *types.Email { if req.Email != nil { e := types.Email(*req.Email); return &e } else { return nil } }(),
		Phone:     req.Phone,
		JobTitle:  req.JobTitle,
		CompanyId: func() *types.UUID { if req.CompanyId != nil { return uuidPtr(req.CompanyId.String()) } else { return nil } }(),
		CreatedBy: uuidPtr(userID.(string)),
		IsDeleted: boolPtr(false),
		CreatedAt: timePtr(now),
		UpdatedAt: timePtr(now),
	}

	_, err := db.Exec(
		`INSERT INTO contacts (id, first_name, last_name, email, phone, job_title, company_id, created_by, is_deleted, created_at, updated_at)
		 VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
		contact.Id, contact.FirstName, contact.LastName, contact.Email, contact.Phone,
		contact.JobTitle, contact.CompanyId, contact.CreatedBy, contact.IsDeleted,
		contact.CreatedAt, contact.UpdatedAt,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create contact"})
		return
	}

	c.JSON(http.StatusCreated, contact)
}

func GetContact(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Contact ID required"})
		return
	}

	db := database.GetDB()
	
	var contact ContactDetails
	var email, phone, jobTitle sql.NullString
	var companyID, companyName sql.NullString

	err := db.QueryRow(
		`SELECT c.id, c.first_name, c.last_name, c.email, c.phone, c.job_title, 
		        c.company_id, c.created_by, c.is_deleted, c.created_at, c.updated_at,
		        comp.id, comp.name
		 FROM contacts c
		 LEFT JOIN companies comp ON c.company_id = comp.id
		 WHERE c.id = $1 AND c.is_deleted = false`,
		id,
	).Scan(&contact.Id, &contact.FirstName, &contact.LastName, &email,
		&phone, &jobTitle, &contact.CompanyId, &contact.CreatedBy,
		&contact.IsDeleted, &contact.CreatedAt, &contact.UpdatedAt,
		&companyID, &companyName)

	// Convert sql.NullString to pointer types for OpenAPI
	contact.Email = func() *types.Email {
		if email.Valid && email.String != "" {
			e := types.Email(email.String)
			return &e
		}
		return nil
	}()
	contact.Phone = nullStringToPtr(phone)
	contact.JobTitle = nullStringToPtr(jobTitle)

	if err == sql.ErrNoRows {
		c.JSON(http.StatusNotFound, gin.H{"error": "Contact not found"})
		return
	}
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}

	if companyID.Valid {
		contact.Company = &openapi.CompanyBasicInfo{
			Id:   nullStringToUUIDPtr(companyID),
			Name: nullStringToPtr(companyName),
		}
	}

	c.JSON(http.StatusOK, contact)
}

func UpdateContact(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Contact ID required"})
		return
	}

	var req ContactUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	db := database.GetDB()
	
	// Build dynamic update query
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	if req.FirstName != nil {
		setParts = append(setParts, "first_name = $"+string(rune(argIndex+'0')))
		args = append(args, *req.FirstName)
		argIndex++
	}
	if req.LastName != nil {
		setParts = append(setParts, "last_name = $"+string(rune(argIndex+'0')))
		args = append(args, *req.LastName)
		argIndex++
	}
	if req.Email != nil {
		setParts = append(setParts, "email = $"+string(rune(argIndex+'0')))
		args = append(args, string(*req.Email))
		argIndex++
	}
	if req.Phone != nil {
		setParts = append(setParts, "phone = $"+string(rune(argIndex+'0')))
		args = append(args, *req.Phone)
		argIndex++
	}
	if req.JobTitle != nil {
		setParts = append(setParts, "job_title = $"+string(rune(argIndex+'0')))
		args = append(args, *req.JobTitle)
		argIndex++
	}
	if req.CompanyId != nil {
		setParts = append(setParts, "company_id = $"+string(rune(argIndex+'0')))
		args = append(args, *req.CompanyId)
		argIndex++
	}

	if len(setParts) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	// Add updated_at
	setParts = append(setParts, "updated_at = $"+string(rune(argIndex+'0')))
	args = append(args, time.Now())
	argIndex++

	// Add WHERE clause
	args = append(args, id)

	query := "UPDATE contacts SET " + setParts[0]
	for i := 1; i < len(setParts); i++ {
		query += ", " + setParts[i]
	}
	query += " WHERE id = $" + string(rune(argIndex+'0')) + " AND is_deleted = false"

	result, err := db.Exec(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update contact"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Contact not found"})
		return
	}

	// Return updated contact
	GetContact(c)
}

func DeleteContact(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Contact ID required"})
		return
	}

	db := database.GetDB()
	
	result, err := db.Exec(
		"UPDATE contacts SET is_deleted = true, updated_at = $1 WHERE id = $2 AND is_deleted = false",
		time.Now(), id,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete contact"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Contact not found"})
		return
	}

	c.Status(http.StatusNoContent)
}

func LinkContactToCompany(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Contact ID required"})
		return
	}

	var req struct {
		CompanyId string `json:"company_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	db := database.GetDB()
	
	result, err := db.Exec(
		"UPDATE contacts SET company_id = $1, updated_at = $2 WHERE id = $3 AND is_deleted = false",
		req.CompanyId, time.Now(), id,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to link contact to company"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Contact not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Contact linked to company successfully"})
}

func UnlinkContactFromCompany(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Contact ID required"})
		return
	}

	db := database.GetDB()
	
	result, err := db.Exec(
		"UPDATE contacts SET company_id = NULL, updated_at = $1 WHERE id = $2 AND is_deleted = false",
		time.Now(), id,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unlink contact from company"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Contact not found"})
		return
	}

	c.Status(http.StatusNoContent)
}